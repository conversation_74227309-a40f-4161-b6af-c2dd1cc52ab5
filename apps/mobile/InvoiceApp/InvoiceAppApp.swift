//
//  InvoiceAppApp.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import SwiftUI
import Supabase

@main
struct InvoiceAppApp: App {
    @StateObject private var authViewModel = AuthViewModel()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(authViewModel)
                .environment(\.layoutDirection, .rightToLeft)
                .preferredColorScheme(.dark)
                .onOpenURL { url in
                    Task {
                        do {
                            try await SupabaseService.shared.client.auth.session(from: url)
                        } catch {
                            print("Failed to handle URL: \(error)")
                        }
                    }
                }
        }
    }
}
