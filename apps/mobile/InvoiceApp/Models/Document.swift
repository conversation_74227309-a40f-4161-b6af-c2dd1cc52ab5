//
//  Document.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import Foundation

// MARK: - Document Models

struct Document: Codable, Identifiable {
    let id: String
    let companyId: String
    let documentType: DocumentType
    let documentNumber: String
    let customerId: String
    let issueDate: Date
    let dueDate: Date?
    let currency: String
    let subtotal: Double
    let vatAmount: Double
    let totalAmount: Double
    let status: DocumentStatus
    let itaAllocationNumber: String?
    let itaAllocationDate: Date?
    let itaSubmissionAttempts: Int
    let itaLastError: String?
    let parentDocumentId: String?
    let notes: String?
    let templateId: String
    let pdfUrl: String?
    let sentAt: Date?
    let sentVia: String?
    let createdBy: String
    let createdAt: Date
    let updatedAt: Date
    
    // Related data
    var customer: Customer?
    var items: [DocumentItem]?
    
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case documentType = "document_type"
        case documentNumber = "document_number"
        case customerId = "customer_id"
        case issueDate = "issue_date"
        case dueDate = "due_date"
        case currency
        case subtotal
        case vatAmount = "vat_amount"
        case totalAmount = "total_amount"
        case status
        case itaAllocationNumber = "ita_allocation_number"
        case itaAllocationDate = "ita_allocation_date"
        case itaSubmissionAttempts = "ita_submission_attempts"
        case itaLastError = "ita_last_error"
        case parentDocumentId = "parent_document_id"
        case notes
        case templateId = "template_id"
        case pdfUrl = "pdf_url"
        case sentAt = "sent_at"
        case sentVia = "sent_via"
        case createdBy = "created_by"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case customer
        case items
    }
}

enum DocumentType: String, Codable, CaseIterable {
    case taxInvoice = "tax_invoice"
    case receipt = "receipt"
    case creditNote = "credit_note"
    case taxInvoiceReceipt = "tax_invoice_receipt"
    
    var displayName: String {
        switch self {
        case .taxInvoice:
            return "חשבונית מס"
        case .receipt:
            return "קבלה"
        case .creditNote:
            return "זיכוי"
        case .taxInvoiceReceipt:
            return "חשבונית מס/קבלה"
        }
    }
    
    var icon: String {
        switch self {
        case .taxInvoice:
            return "doc.text"
        case .receipt:
            return "receipt"
        case .creditNote:
            return "minus.circle"
        case .taxInvoiceReceipt:
            return "doc.text.below.ecg"
        }
    }
}

enum DocumentStatus: String, Codable, CaseIterable {
    case draft = "draft"
    case pendingAllocation = "pending_allocation"
    case approved = "approved"
    case sent = "sent"
    case paid = "paid"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .draft:
            return "טיוטה"
        case .pendingAllocation:
            return "ממתין לאישור"
        case .approved:
            return "אושר"
        case .sent:
            return "נשלח"
        case .paid:
            return "שולם"
        case .cancelled:
            return "בוטל"
        }
    }
    
    var color: String {
        switch self {
        case .draft:
            return "gray"
        case .pendingAllocation:
            return "orange"
        case .approved:
            return "blue"
        case .sent:
            return "purple"
        case .paid:
            return "green"
        case .cancelled:
            return "red"
        }
    }
}

// MARK: - Document Item

struct DocumentItem: Codable, Identifiable {
    let id: String
    let documentId: String
    let productId: String?
    let lineNumber: Int
    let descriptionHebrew: String
    let descriptionEnglish: String?
    let quantity: Double
    let unitPrice: Double
    let currency: String
    let discountPercent: Double
    let vatRate: Double
    let lineTotal: Double
    let vatAmount: Double
    let totalWithVat: Double
    
    enum CodingKeys: String, CodingKey {
        case id
        case documentId = "document_id"
        case productId = "product_id"
        case lineNumber = "line_number"
        case descriptionHebrew = "description_hebrew"
        case descriptionEnglish = "description_english"
        case quantity
        case unitPrice = "unit_price"
        case currency
        case discountPercent = "discount_percent"
        case vatRate = "vat_rate"
        case lineTotal = "line_total"
        case vatAmount = "vat_amount"
        case totalWithVat = "total_with_vat"
    }
}

// MARK: - Customer Model

struct Customer: Codable, Identifiable {
    let id: String
    let companyId: String
    let businessNumber: String
    let nameHebrew: String
    let nameEnglish: String?
    let vatId: String?
    let billingAddressHebrew: String
    let billingAddressEnglish: String?
    let shippingAddressHebrew: String?
    let shippingAddressEnglish: String?
    let cityHebrew: String
    let cityEnglish: String?
    let contactName: String?
    let contactEmail: String?
    let contactPhone: String?
    let notes: String?
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case businessNumber = "business_number"
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case vatId = "vat_id"
        case billingAddressHebrew = "billing_address_hebrew"
        case billingAddressEnglish = "billing_address_english"
        case shippingAddressHebrew = "shipping_address_hebrew"
        case shippingAddressEnglish = "shipping_address_english"
        case cityHebrew = "city_hebrew"
        case cityEnglish = "city_english"
        case contactName = "contact_name"
        case contactEmail = "contact_email"
        case contactPhone = "contact_phone"
        case notes
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Product Model

struct Product: Codable, Identifiable {
    let id: String
    let companyId: String
    let sku: String?
    let nameHebrew: String
    let nameEnglish: String?
    let descriptionHebrew: String?
    let descriptionEnglish: String?
    let unitPrice: Double
    let currency: String
    let vatRate: Double
    let isService: Bool
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case sku
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case descriptionHebrew = "description_hebrew"
        case descriptionEnglish = "description_english"
        case unitPrice = "unit_price"
        case currency
        case vatRate = "vat_rate"
        case isService = "is_service"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Expense Model

struct Expense: Codable, Identifiable {
    let id: String
    let companyId: String
    let expenseNumber: String
    let vendorName: String
    let expenseDate: Date
    let amount: Double
    let vatAmount: Double
    let totalAmount: Double
    let currency: String
    let category: ExpenseCategory
    let description: String?
    let status: ExpenseStatus
    let duplicateRisk: DuplicateRisk
    let duplicateOfId: String?
    let source: ExpenseSource
    let sourceEmailId: String?
    let originalFileUrl: String?
    let extractedData: [String: Any]?
    let approvedBy: String?
    let approvedAt: Date?
    let rejectionReason: String?
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case expenseNumber = "expense_number"
        case vendorName = "vendor_name"
        case expenseDate = "expense_date"
        case amount
        case vatAmount = "vat_amount"
        case totalAmount = "total_amount"
        case currency
        case category
        case description
        case status
        case duplicateRisk = "duplicate_risk"
        case duplicateOfId = "duplicate_of_id"
        case source
        case sourceEmailId = "source_email_id"
        case originalFileUrl = "original_file_url"
        case extractedData = "extracted_data"
        case approvedBy = "approved_by"
        case approvedAt = "approved_at"
        case rejectionReason = "rejection_reason"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

enum ExpenseCategory: String, Codable, CaseIterable {
    case officeSupplies = "office_supplies"
    case travel = "travel"
    case utilities = "utilities"
    case rent = "rent"
    case professionalServices = "professional_services"
    case marketing = "marketing"
    case equipment = "equipment"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .officeSupplies: return "ציוד משרדי"
        case .travel: return "נסיעות"
        case .utilities: return "שירותים"
        case .rent: return "שכירות"
        case .professionalServices: return "שירותים מקצועיים"
        case .marketing: return "שיווק"
        case .equipment: return "ציוד"
        case .other: return "אחר"
        }
    }
}

enum ExpenseStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case approved = "approved"
    case rejected = "rejected"
    
    var displayName: String {
        switch self {
        case .pending: return "ממתין"
        case .approved: return "אושר"
        case .rejected: return "נדחה"
        }
    }
}

enum DuplicateRisk: String, Codable, CaseIterable {
    case none = "none"
    case low = "low"
    case high = "high"
    
    var displayName: String {
        switch self {
        case .none: return "אין"
        case .low: return "נמוך"
        case .high: return "גבוה"
        }
    }
}

enum ExpenseSource: String, Codable, CaseIterable {
    case manual = "manual"
    case emailScan = "email_scan"
    case upload = "upload"
    
    var displayName: String {
        switch self {
        case .manual: return "ידני"
        case .emailScan: return "סריקת אימייל"
        case .upload: return "העלאה"
        }
    }
}
