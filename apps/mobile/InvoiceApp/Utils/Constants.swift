//
//  Constants.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import Foundation

struct Constants {
    
    // MARK: - Supabase Configuration
    struct Supabase {
        static let url = ProcessInfo.processInfo.environment["SUPABASE_URL"] ?? "https://your-project.supabase.co"
        static let anonKey = ProcessInfo.processInfo.environment["SUPABASE_ANON_KEY"] ?? "your-anon-key"
        static let redirectURL = URL(string: "com.fintech.invoiceapp://auth")!
    }
    
    // MARK: - API Endpoints
    struct API {
        static let baseURL = "YOUR_SUPABASE_URL/functions/v1"
        static let authLogin = "/auth-login"
        static let authRegister = "/auth-register"
        static let documents = "/documents"
        static let customers = "/customers"
        static let expenses = "/expenses"
    }
    
    // MARK: - App Configuration
    struct App {
        static let name = "חשבוניות"
        static let version = "1.0.0"
        static let bundleIdentifier = "com.fintech.invoiceapp"
        static let minimumIOSVersion = "15.0"
    }
    
    // MARK: - User Defaults Keys
    struct UserDefaults {
        static let isFirstLaunch = "isFirstLaunch"
        static let userSession = "userSession"
        static let selectedCompanyId = "selectedCompanyId"
        static let biometricEnabled = "biometricEnabled"
        static let notificationsEnabled = "notificationsEnabled"
    }
    
    // MARK: - Validation Rules
    struct Validation {
        static let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        static let phoneRegex = "^(\\+972|0)(5[0-9]|7[1-9])[0-9]{7}$"
        static let businessNumberRegex = "^[0-9]{9}$"
        static let vatIdRegex = "^[0-9]{9}$"
        static let passwordMinLength = 8
    }
    
    // MARK: - Animation Durations
    struct Animation {
        static let quick: Double = 0.15
        static let standard: Double = 0.2
        static let slow: Double = 0.3
        static let pageTransition: Double = 0.4
    }
    
    // MARK: - Layout Constants
    struct Layout {
        static let cardPadding: CGFloat = 24
        static let sectionSpacing: CGFloat = 32
        static let buttonHeight: CGFloat = 48
        static let inputHeight: CGFloat = 44
        static let cornerRadius: CGFloat = 12
        static let borderWidth: CGFloat = 1
    }
    
    // MARK: - Hebrew Text
    struct HebrewText {
        // Authentication
        static let login = "התחברות"
        static let register = "הרשמה"
        static let email = "כתובת אימייל"
        static let password = "סיסמה"
        static let fullName = "שם מלא"
        static let phone = "טלפון"
        static let loginButton = "התחבר"
        static let registerButton = "הירשם"
        static let forgotPassword = "שכחת סיסמה?"
        static let noAccount = "אין לך חשבון?"
        static let hasAccount = "יש לך חשבון?"
        static let signUpHere = "הירשם כאן"
        static let signInHere = "התחבר כאן"
        
        // Company Information
        static let companyInfo = "פרטי החברה"
        static let companyName = "שם החברה"
        static let companyNameHebrew = "שם החברה בעברית"
        static let companyNameEnglish = "שם החברה באנגלית"
        static let businessNumber = "ח.פ / ע.מ"
        static let vatId = "מספר עוסק מורשה"
        static let address = "כתובת"
        static let city = "עיר"
        
        // Survey
        static let survey = "סקר קצר"
        static let industry = "תחום עיסוק"
        static let annualRevenue = "מחזור שנתי"
        static let interestedInLoan = "מעוניין בהלוואה עסקית"
        static let interestedInInsurance = "מעוניין בביטוח עסקי"
        static let interestedInAccounting = "מעוניין בשירותי הנהלת חשבונות"
        static let yes = "כן"
        static let no = "לא"
        
        // Navigation
        static let next = "הבא"
        static let previous = "הקודם"
        static let submit = "שלח"
        static let cancel = "ביטול"
        static let save = "שמור"
        static let edit = "ערוך"
        static let delete = "מחק"
        
        // Dashboard
        static let dashboard = "לוח בקרה"
        static let thisMonth = "החודש"
        static let vatPayable = "מע״מ לתשלום"
        static let openInvoices = "חשבוניות פתוחות"
        static let pendingExpenses = "הוצאות ממתינות"
        static let newInvoice = "חשבונית חדשה"
        static let scanExpense = "סרוק הוצאה"
        
        // Documents
        static let documents = "מסמכים"
        static let invoices = "חשבוניות"
        static let receipts = "קבלות"
        static let creditNotes = "זיכויים"
        static let customers = "לקוחות"
        static let products = "מוצרים"
        static let expenses = "הוצאות"
        static let reports = "דוחות"
        static let settings = "הגדרות"
        
        // Error Messages
        static let errorTitle = "שגיאה"
        static let networkError = "בעיית רשת. אנא נסה שוב."
        static let invalidEmail = "כתובת אימייל לא תקינה"
        static let invalidPassword = "סיסמה חייבת להכיל לפחות 8 תווים"
        static let invalidPhone = "מספר טלפון לא תקין"
        static let invalidBusinessNumber = "מספר עסק חייב להכיל 9 ספרות"
        static let fieldRequired = "שדה חובה"
        static let loginFailed = "התחברות נכשלה. אנא בדוק את הפרטים."
        static let registrationFailed = "הרשמה נכשלה. אנא נסה שוב."
        
        // Success Messages
        static let loginSuccess = "התחברת בהצלחה"
        static let registrationSuccess = "נרשמת בהצלחה"
        static let documentCreated = "המסמך נוצר בהצלחה"
        static let documentSent = "המסמך נשלח בהצלחה"
    }
    
    // MARK: - Industry Options
    static let industryOptions = [
        "טכנולוגיה",
        "קמעונאות",
        "שירותים",
        "ייצור",
        "בנייה",
        "בריאות",
        "חינוך",
        "תיירות",
        "מזון ומשקאות",
        "אחר"
    ]
    
    // MARK: - Annual Revenue Options
    static let annualRevenueOptions = [
        "עד 500 אלף ₪",
        "500 אלף - 1 מיליון ₪",
        "1-5 מיליון ₪",
        "5-10 מיליון ₪",
        "10-50 מיליון ₪",
        "מעל 50 מיליון ₪"
    ]
}
