//
//  DesignSystem.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import SwiftUI

struct DesignSystem {
    
    // MARK: - Colors
    struct Colors {
        // Dark Theme (Default)
        static let background = Color(red: 0.08, green: 0.08, blue: 0.08) // #141414
        static let foreground = Color(red: 0.95, green: 0.95, blue: 0.95) // #F2F2F2
        static let card = Color(red: 0.12, green: 0.12, blue: 0.12) // #1F1F1F
        static let cardForeground = Color(red: 0.95, green: 0.95, blue: 0.95) // #F2F2F2
        static let primary = Color(red: 0.85, green: 0.85, blue: 0.85) // #D9D9D9
        static let primaryForeground = Color(red: 0.15, green: 0.15, blue: 0.15) // #262626
        static let secondary = Color(red: 0.18, green: 0.18, blue: 0.18) // #2E2E2E
        static let secondaryForeground = Color(red: 0.95, green: 0.95, blue: 0.95) // #F2F2F2
        static let muted = Color(red: 0.22, green: 0.22, blue: 0.22) // #383838
        static let mutedForeground = Color(red: 0.70, green: 0.70, blue: 0.70) // #B3B3B3
        static let accent = Color(red: 0.70, green: 0.70, blue: 0.70) // #B3B3B3
        static let accentForeground = Color(red: 0.15, green: 0.15, blue: 0.15) // #262626
        static let border = Color(red: 0.22, green: 0.22, blue: 0.22) // #383838
        static let input = Color(red: 0.22, green: 0.22, blue: 0.22) // #383838
        static let ring = Color(red: 0.70, green: 0.70, blue: 0.70) // #B3B3B3
        
        // State Colors
        static let destructive = Color(red: 0.96, green: 0.40, blue: 0.40) // #F56565
        static let destructiveForeground = Color(red: 0.97, green: 0.98, blue: 0.99) // #F7FAFC
        
        // Cosmic Accent Colors
        static let cosmicDark = Color(red: 0.25, green: 0.25, blue: 0.25) // #404040
        static let cosmicDarker = Color(red: 0.19, green: 0.19, blue: 0.19) // #303030
        static let cosmicLight = Color(red: 0.94, green: 0.94, blue: 0.94) // #f0f0f0
        static let cosmicAccent = Color(red: 0.38, green: 0.38, blue: 0.38) // #606060
        static let cosmicMuted = Color(red: 0.56, green: 0.56, blue: 0.56) // #909090
    }
    
    // MARK: - Typography
    struct Typography {
        // Font Names
        static let hebrewFont = "SecularOne-Regular"
        static let latinFont = "Inter"
        
        // Font Sizes
        static let title1 = Font.custom(hebrewFont, size: 36).weight(.bold)
        static let title2 = Font.custom(hebrewFont, size: 30).weight(.bold)
        static let title3 = Font.custom(hebrewFont, size: 24).weight(.semibold)
        static let headline = Font.custom(hebrewFont, size: 20).weight(.semibold)
        static let body = Font.custom(hebrewFont, size: 16).weight(.regular)
        static let bodyLarge = Font.custom(hebrewFont, size: 18).weight(.regular)
        static let caption = Font.custom(hebrewFont, size: 14).weight(.regular)
        static let caption2 = Font.custom(hebrewFont, size: 12).weight(.regular)
        
        // Line Heights
        static let tightLineSpacing: CGFloat = 1.25
        static let normalLineSpacing: CGFloat = 1.5
        static let relaxedLineSpacing: CGFloat = 1.625
    }
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
        static let xxxl: CGFloat = 32
        static let huge: CGFloat = 40
        static let massive: CGFloat = 48
        static let giant: CGFloat = 64
        static let colossal: CGFloat = 80
        static let enormous: CGFloat = 96
    }
    
    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 6
        static let medium: CGFloat = 8
        static let large: CGFloat = 12
        static let extraLarge: CGFloat = 16
        static let pill: CGFloat = 9999
    }
    
    // MARK: - Shadows
    struct Shadows {
        static let small = Shadow(color: .black.opacity(0.05), radius: 1, x: 0, y: 1)
        static let medium = Shadow(color: .black.opacity(0.1), radius: 3, x: 0, y: 1)
        static let large = Shadow(color: .black.opacity(0.1), radius: 6, x: 0, y: 4)
        static let extraLarge = Shadow(color: .black.opacity(0.1), radius: 15, x: 0, y: 10)
        
        // Cosmic Glow
        static let cosmicGlow = Shadow(color: .white.opacity(0.2), radius: 20, x: 0, y: 0)
        static let pricingGlow = Shadow(color: primary.opacity(0.15), radius: 20, x: 0, y: 0)
    }
    
    // MARK: - Animation
    struct Animation {
        static let quick = SwiftUI.Animation.easeInOut(duration: 0.15)
        static let standard = SwiftUI.Animation.easeInOut(duration: 0.2)
        static let slow = SwiftUI.Animation.easeInOut(duration: 0.3)
        static let bounce = SwiftUI.Animation.spring(response: 0.6, dampingFraction: 0.8)
    }
}

// MARK: - Shadow Helper
struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - View Extensions
extension View {
    func cosmicCard() -> some View {
        self
            .background(DesignSystem.Colors.card)
            .cornerRadius(DesignSystem.CornerRadius.large)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .stroke(DesignSystem.Colors.border, lineWidth: 1)
            )
    }
    
    func cosmicGlass() -> some View {
        self
            .background(.ultraThinMaterial)
            .cornerRadius(DesignSystem.CornerRadius.large)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
    }
    
    func primaryButton() -> some View {
        self
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.primary)
            .foregroundColor(DesignSystem.Colors.primaryForeground)
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .font(DesignSystem.Typography.body.weight(.medium))
    }
    
    func secondaryButton() -> some View {
        self
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.secondary)
            .foregroundColor(DesignSystem.Colors.secondaryForeground)
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .font(DesignSystem.Typography.body.weight(.medium))
    }
    
    func inputField() -> some View {
        self
            .padding(DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.input)
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(DesignSystem.Colors.border, lineWidth: 1)
            )
    }
}
