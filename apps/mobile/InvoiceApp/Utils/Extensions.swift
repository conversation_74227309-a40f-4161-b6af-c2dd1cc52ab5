//
//  Extensions.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import SwiftUI
import Foundation

// MARK: - View Extensions

extension View {
    /// Hide keyboard when tapping outside
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    /// Apply cosmic glow effect on hover/press
    func cosmicGlow() -> some View {
        self.overlay(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                .stroke(Color.white.opacity(0.1), lineWidth: 1)
        )
        .shadow(color: .white.opacity(0.1), radius: 10, x: 0, y: 0)
    }
    
    /// Apply RTL layout direction
    func rtlLayout() -> some View {
        self.environment(\.layoutDirection, .rightToLeft)
    }
    
    /// Conditional view modifier
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
    
    /// Apply loading overlay
    func loadingOverlay(_ isLoading: Bool) -> some View {
        self.overlay(
            Group {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.3)
                            .ignoresSafeArea()
                        
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))
                            .scaleEffect(1.5)
                    }
                }
            }
        )
    }
}

// MARK: - String Extensions

extension String {
    /// Check if string is a valid email
    var isValidEmail: Bool {
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", Constants.Validation.emailRegex)
        return emailPredicate.evaluate(with: self)
    }
    
    /// Check if string is a valid Israeli phone number
    var isValidIsraeliPhone: Bool {
        let phonePredicate = NSPredicate(format: "SELF MATCHES %@", Constants.Validation.phoneRegex)
        return phonePredicate.evaluate(with: self)
    }
    
    /// Check if string is a valid Israeli business number
    var isValidBusinessNumber: Bool {
        let businessPredicate = NSPredicate(format: "SELF MATCHES %@", Constants.Validation.businessNumberRegex)
        return businessPredicate.evaluate(with: self)
    }
    
    /// Check if string is a valid VAT ID
    var isValidVatId: Bool {
        let vatPredicate = NSPredicate(format: "SELF MATCHES %@", Constants.Validation.vatIdRegex)
        return vatPredicate.evaluate(with: self)
    }
    
    /// Remove whitespace and newlines
    var trimmed: String {
        return self.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// Convert to Hebrew numerals if needed
    var hebrewNumerals: String {
        let arabicNumerals = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]
        let hebrewNumerals = ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"]
        
        var result = self
        for (arabic, hebrew) in zip(arabicNumerals, hebrewNumerals) {
            result = result.replacingOccurrences(of: arabic, with: hebrew)
        }
        return result
    }
    
    /// Localized string lookup
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
}

// MARK: - Date Extensions

extension Date {
    /// Format date for Hebrew locale
    func hebrewFormatted(style: DateFormatter.Style = .medium) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = style
        formatter.locale = Locale(identifier: "he_IL")
        formatter.calendar = Calendar(identifier: .hebrew)
        return formatter.string(from: self)
    }
    
    /// Format date for API (ISO 8601)
    func apiFormatted() -> String {
        let formatter = ISO8601DateFormatter()
        return formatter.string(from: self)
    }
    
    /// Check if date is today
    var isToday: Bool {
        return Calendar.current.isDateInToday(self)
    }
    
    /// Check if date is yesterday
    var isYesterday: Bool {
        return Calendar.current.isDateInYesterday(self)
    }
    
    /// Get relative date string in Hebrew
    var relativeHebrewString: String {
        if isToday {
            return "היום"
        } else if isYesterday {
            return "אתמול"
        } else {
            let formatter = RelativeDateTimeFormatter()
            formatter.locale = Locale(identifier: "he_IL")
            return formatter.localizedString(for: self, relativeTo: Date())
        }
    }
}

// MARK: - Double Extensions

extension Double {
    /// Format as Israeli currency
    func formatAsILS() -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        formatter.maximumFractionDigits = 2
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: NSNumber(value: self)) ?? "₪0"
    }
    
    /// Format as percentage
    func formatAsPercentage() -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .percent
        formatter.maximumFractionDigits = 1
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: NSNumber(value: self / 100)) ?? "0%"
    }
    
    /// Round to specified decimal places
    func rounded(toPlaces places: Int) -> Double {
        let divisor = pow(10.0, Double(places))
        return (self * divisor).rounded() / divisor
    }
}

// MARK: - Color Extensions

extension Color {
    /// Initialize color from hex string
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    /// Get hex string representation
    var hexString: String {
        guard let components = cgColor?.components, components.count >= 3 else {
            return "#000000"
        }
        
        let r = Float(components[0])
        let g = Float(components[1])
        let b = Float(components[2])
        
        return String(format: "#%02lX%02lX%02lX",
                     lroundf(r * 255),
                     lroundf(g * 255),
                     lroundf(b * 255))
    }
}

// MARK: - Array Extensions

extension Array {
    /// Safe subscript access
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

// MARK: - UserDefaults Extensions

extension UserDefaults {
    /// Save Codable object
    func set<T: Codable>(_ object: T, forKey key: String) {
        if let encoded = try? JSONEncoder().encode(object) {
            set(encoded, forKey: key)
        }
    }
    
    /// Retrieve Codable object
    func get<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
        guard let data = data(forKey: key) else { return nil }
        return try? JSONDecoder().decode(type, from: data)
    }
}

// MARK: - Bundle Extensions

extension Bundle {
    /// Get app version
    var appVersion: String {
        return infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
    
    /// Get build number
    var buildNumber: String {
        return infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }
    
    /// Get app name
    var appName: String {
        return infoDictionary?["CFBundleDisplayName"] as? String ?? 
               infoDictionary?["CFBundleName"] as? String ?? "InvoiceApp"
    }
}

// MARK: - UIApplication Extensions

extension UIApplication {
    /// Get key window
    var keyWindow: UIWindow? {
        return connectedScenes
            .compactMap { $0 as? UIWindowScene }
            .flatMap { $0.windows }
            .first { $0.isKeyWindow }
    }
    
    /// Get safe area insets
    var safeAreaInsets: UIEdgeInsets {
        return keyWindow?.safeAreaInsets ?? .zero
    }
}
