//
//  ProfileView.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import SwiftUI

struct ProfileView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showingSignOutAlert = false
    
    var body: some View {
        NavigationView {
            ZStack {
                DesignSystem.Colors.background
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.xxl) {
                        // User Info Section
                        userInfoSection
                        
                        // Company Selection Section
                        if authViewModel.userCompanies.count > 1 {
                            companySelectionSection
                        }
                        
                        // Settings Section
                        settingsSection
                        
                        // Sign Out Section
                        signOutSection
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.top, DesignSystem.Spacing.lg)
                }
            }
            .navigationTitle("פרופיל")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("סגור") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.primary)
                }
            }
        }
        .alert("התנתקות", isPresented: $showingSignOutAlert) {
            But<PERSON>("ביטול", role: .cancel) { }
            Button("התנתק", role: .destructive) {
                Task {
                    await authViewModel.signOut()
                    dismiss()
                }
            }
        } message: {
            Text("האם אתה בטוח שברצונך להתנתק?")
        }
    }
    
    // MARK: - User Info Section
    
    private var userInfoSection: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // Profile Picture Placeholder
            Circle()
                .fill(DesignSystem.Colors.primary)
                .frame(width: 80, height: 80)
                .overlay(
                    Text(userInitials)
                        .font(DesignSystem.Typography.title2)
                        .foregroundColor(DesignSystem.Colors.primaryForeground)
                )
            
            VStack(spacing: DesignSystem.Spacing.sm) {
                if let user = authViewModel.currentUser {
                    Text(user.full_name)
                        .font(DesignSystem.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.foreground)
                    
                    Text(user.email)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                    
                    if let phone = user.phone {
                        Text(phone)
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.mutedForeground)
                    }
                }
            }
        }
        .padding(DesignSystem.Spacing.xxl)
        .cosmicCard()
    }
    
    // MARK: - Company Selection Section
    
    private var companySelectionSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            Text("החברות שלי")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.md) {
                ForEach(authViewModel.userCompanies, id: \.company.id) { companyWithRole in
                    CompanyRow(
                        company: companyWithRole.company,
                        role: companyWithRole.role,
                        isSelected: authViewModel.selectedCompany?.id == companyWithRole.company.id
                    ) {
                        authViewModel.selectCompany(companyWithRole.company)
                    }
                }
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
    
    // MARK: - Settings Section
    
    private var settingsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            Text("הגדרות")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.sm) {
                SettingsRow(
                    icon: "bell",
                    title: "התראות",
                    subtitle: "נהל התראות האפליקציה"
                ) {
                    // Handle notifications settings
                }
                
                SettingsRow(
                    icon: "faceid",
                    title: "זיהוי ביומטרי",
                    subtitle: "התחבר עם Face ID או Touch ID"
                ) {
                    // Handle biometric settings
                }
                
                SettingsRow(
                    icon: "questionmark.circle",
                    title: "עזרה ותמיכה",
                    subtitle: "קבל עזרה ופתרונות"
                ) {
                    // Handle help
                }
                
                SettingsRow(
                    icon: "doc.text",
                    title: "תנאי שימוש",
                    subtitle: "קרא את תנאי השימוש"
                ) {
                    // Handle terms
                }
                
                SettingsRow(
                    icon: "hand.raised",
                    title: "מדיניות פרטיות",
                    subtitle: "קרא את מדיניות הפרטיות"
                ) {
                    // Handle privacy policy
                }
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
    
    // MARK: - Sign Out Section
    
    private var signOutSection: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Button(action: {
                showingSignOutAlert = true
            }) {
                HStack {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .foregroundColor(DesignSystem.Colors.destructive)
                    
                    Spacer()
                    
                    Text("התנתק")
                        .font(DesignSystem.Typography.body.weight(.medium))
                        .foregroundColor(DesignSystem.Colors.destructive)
                }
                .padding(DesignSystem.Spacing.lg)
                .background(DesignSystem.Colors.card)
                .cornerRadius(DesignSystem.CornerRadius.medium)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                        .stroke(DesignSystem.Colors.destructive.opacity(0.3), lineWidth: 1)
                )
            }
            
            // App Version
            VStack(spacing: DesignSystem.Spacing.xs) {
                Text("גרסה \(Bundle.main.appVersion)")
                    .font(DesignSystem.Typography.caption2)
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                
                Text("בילד \(Bundle.main.buildNumber)")
                    .font(DesignSystem.Typography.caption2)
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var userInitials: String {
        guard let user = authViewModel.currentUser else { return "?" }
        let names = user.full_name.split(separator: " ")
        if names.count >= 2 {
            return String(names[0].prefix(1)) + String(names[1].prefix(1))
        } else if let firstChar = names.first?.first {
            return String(firstChar)
        }
        return "?"
    }
}

// MARK: - Supporting Views

struct CompanyRow: View {
    let company: Company
    let role: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                    Text(roleDisplayName)
                        .font(DesignSystem.Typography.caption2)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                    
                    Text(company.business_number)
                        .font(DesignSystem.Typography.caption2)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                    Text(company.name_hebrew)
                        .font(DesignSystem.Typography.body.weight(.medium))
                        .foregroundColor(DesignSystem.Colors.foreground)
                    
                    Text(company.industry)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                }
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(DesignSystem.Colors.primary)
                }
            }
            .padding(DesignSystem.Spacing.md)
            .background(isSelected ? DesignSystem.Colors.primary.opacity(0.1) : DesignSystem.Colors.secondary)
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.border, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var roleDisplayName: String {
        switch role {
        case "admin": return "מנהל"
        case "user": return "משתמש"
        case "accountant": return "רואה חשבון"
        default: return role
        }
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: "chevron.left")
                    .font(.caption)
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                    Text(title)
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.foreground)
                    
                    Text(subtitle)
                        .font(DesignSystem.Typography.caption2)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                }
                
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(DesignSystem.Colors.primary)
                    .frame(width: 24, height: 24)
            }
            .padding(DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.secondary)
            .cornerRadius(DesignSystem.CornerRadius.medium)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ProfileView()
        .environmentObject(AuthViewModel())
}
