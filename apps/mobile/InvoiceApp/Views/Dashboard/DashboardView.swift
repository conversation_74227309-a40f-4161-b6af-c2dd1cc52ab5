//
//  DashboardView.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import SwiftUI

struct DashboardView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var dashboardViewModel = DashboardViewModel()
    @State private var showingProfile = false
    @State private var showingCreateDocument = false
    @State private var showingScanExpense = false
    
    var body: some View {
        NavigationView {
            ZStack {
                DesignSystem.Colors.background
                    .ignoresSafeArea()
                
                ScrollView {
                    LazyVStack(spacing: DesignSystem.Spacing.xxl) {
                        // Stats Cards
                        statsSection
                        
                        // Quick Actions
                        quickActionsSection
                        
                        // Recent Documents
                        recentDocumentsSection
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.top, DesignSystem.Spacing.lg)
                }
                .refreshable {
                    await dashboardViewModel.refreshData()
                }
            }
            .navigationTitle(Constants.HebrewText.dashboard)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingProfile = true
                    }) {
                        HStack(spacing: DesignSystem.Spacing.sm) {
                            if dashboardViewModel.pendingExpensesCount > 0 {
                                ZStack {
                                    Circle()
                                        .fill(DesignSystem.Colors.destructive)
                                        .frame(width: 20, height: 20)
                                    
                                    Text("\(dashboardViewModel.pendingExpensesCount)")
                                        .font(.caption2)
                                        .foregroundColor(.white)
                                        .fontWeight(.bold)
                                }
                            }
                            
                            Image(systemName: "person.circle")
                                .font(.title2)
                                .foregroundColor(DesignSystem.Colors.primary)
                        }
                    }
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    if let company = authViewModel.selectedCompany {
                        VStack(alignment: .trailing, spacing: 2) {
                            Text(company.name_hebrew)
                                .font(DesignSystem.Typography.caption.weight(.medium))
                                .foregroundColor(DesignSystem.Colors.foreground)
                            
                            if authViewModel.userCompanies.count > 1 {
                                Text("החלף חברה")
                                    .font(.caption2)
                                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                            }
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showingProfile) {
            ProfileView()
        }
        .sheet(isPresented: $showingCreateDocument) {
            CreateDocumentView()
        }
        .sheet(isPresented: $showingScanExpense) {
            ScanExpenseView()
        }
        .onAppear {
            Task {
                await dashboardViewModel.loadData()
            }
        }
    }
    
    // MARK: - Stats Section
    
    private var statsSection: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: DesignSystem.Spacing.lg) {
            StatCard(
                title: Constants.HebrewText.thisMonth,
                value: dashboardViewModel.monthlyRevenue,
                subtitle: dashboardViewModel.revenueTrend,
                icon: "chart.line.uptrend.xyaxis",
                color: DesignSystem.Colors.primary
            )
            
            StatCard(
                title: Constants.HebrewText.vatPayable,
                value: dashboardViewModel.vatLiability,
                subtitle: dashboardViewModel.nextPaymentDate,
                icon: "percent",
                color: DesignSystem.Colors.cosmicAccent
            )
            
            StatCard(
                title: Constants.HebrewText.openInvoices,
                value: "\(dashboardViewModel.openInvoicesCount)",
                subtitle: dashboardViewModel.openInvoicesAmount,
                icon: "doc.text",
                color: DesignSystem.Colors.primary
            )
            
            StatCard(
                title: Constants.HebrewText.pendingExpenses,
                value: "\(dashboardViewModel.pendingExpensesCount)",
                subtitle: "לבדיקה",
                icon: "creditcard",
                color: dashboardViewModel.pendingExpensesCount > 0 ? DesignSystem.Colors.destructive : DesignSystem.Colors.cosmicAccent
            )
        }
    }
    
    // MARK: - Quick Actions Section
    
    private var quickActionsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            Text("פעולות מהירות")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            HStack(spacing: DesignSystem.Spacing.lg) {
                QuickActionButton(
                    icon: "doc.text.fill",
                    title: Constants.HebrewText.newInvoice,
                    action: {
                        showingCreateDocument = true
                    }
                )
                
                QuickActionButton(
                    icon: "camera.fill",
                    title: Constants.HebrewText.scanExpense,
                    action: {
                        showingScanExpense = true
                    }
                )
            }
        }
    }
    
    // MARK: - Recent Documents Section
    
    private var recentDocumentsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            HStack {
                NavigationLink("הצג הכל") {
                    DocumentsListView()
                }
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.primary)
                
                Spacer()
                
                Text("מסמכים אחרונים")
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.foreground)
            }
            
            if dashboardViewModel.recentDocuments.isEmpty {
                VStack(spacing: DesignSystem.Spacing.md) {
                    Image(systemName: "doc.text")
                        .font(.system(size: 48))
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                    
                    Text("אין מסמכים עדיין")
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                    
                    Text("צור את המסמך הראשון שלך")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                }
                .frame(maxWidth: .infinity)
                .padding(DesignSystem.Spacing.xxl)
                .cosmicCard()
            } else {
                LazyVStack(spacing: DesignSystem.Spacing.md) {
                    ForEach(dashboardViewModel.recentDocuments, id: \.id) { document in
                        DocumentRow(document: document)
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct StatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.md) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Spacer()
                
                Text(title)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
            }
            
            VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                Text(value)
                    .font(DesignSystem.Typography.title3)
                    .foregroundColor(DesignSystem.Colors.foreground)
                    .fontWeight(.bold)
                
                Text(subtitle)
                    .font(DesignSystem.Typography.caption2)
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
}

struct QuickActionButton: View {
    let icon: String
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: icon)
                    .font(.system(size: 32))
                    .foregroundColor(DesignSystem.Colors.primary)
                
                Text(title)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.foreground)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(DesignSystem.Spacing.lg)
            .cosmicCard()
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct DocumentRow: View {
    let document: DocumentSummary
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                Text(document.customerName)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                
                Text(document.formattedDate)
                    .font(DesignSystem.Typography.caption2)
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                Text(document.documentNumber)
                    .font(DesignSystem.Typography.body.weight(.medium))
                    .foregroundColor(DesignSystem.Colors.foreground)
                
                Text(document.formattedAmount)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.primary)
            }
            
            StatusBadge(status: document.status)
            
            Image(systemName: document.typeIcon)
                .font(.title3)
                .foregroundColor(DesignSystem.Colors.primary)
        }
        .padding(DesignSystem.Spacing.md)
        .cosmicCard()
    }
}

struct StatusBadge: View {
    let status: String
    
    var body: some View {
        Text(statusText)
            .font(.caption2)
            .fontWeight(.medium)
            .padding(.horizontal, DesignSystem.Spacing.sm)
            .padding(.vertical, DesignSystem.Spacing.xs)
            .background(statusColor)
            .foregroundColor(.white)
            .cornerRadius(DesignSystem.CornerRadius.pill)
    }
    
    private var statusText: String {
        switch status {
        case "draft": return "טיוטה"
        case "pending_allocation": return "ממתין"
        case "approved": return "אושר"
        case "sent": return "נשלח"
        case "paid": return "שולם"
        case "cancelled": return "בוטל"
        default: return status
        }
    }
    
    private var statusColor: Color {
        switch status {
        case "draft": return DesignSystem.Colors.mutedForeground
        case "pending_allocation": return DesignSystem.Colors.cosmicAccent
        case "approved": return DesignSystem.Colors.primary
        case "sent": return Color.blue
        case "paid": return Color.green
        case "cancelled": return DesignSystem.Colors.destructive
        default: return DesignSystem.Colors.mutedForeground
        }
    }
}

#Preview {
    DashboardView()
        .environmentObject(AuthViewModel())
}
