//
//  ScanExpenseView.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import SwiftUI
import AVFoundation
import Vision

struct ScanExpenseView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var cameraManager = CameraManager()
    @State private var showingImagePicker = false
    @State private var showingExpenseForm = false
    @State private var extractedExpense: ExtractedExpenseData?
    @State private var isProcessing = false
    
    var body: some View {
        NavigationView {
            ZStack {
                DesignSystem.Colors.background
                    .ignoresSafeArea()
                
                VStack(spacing: DesignSystem.Spacing.xxl) {
                    // Header
                    VStack(spacing: DesignSystem.Spacing.md) {
                        Image(systemName: "camera.fill")
                            .font(.system(size: 48))
                            .foregroundColor(DesignSystem.Colors.primary)
                        
                        Text("סרוק קבלה")
                            .font(DesignSystem.Typography.title2)
                            .foregroundColor(DesignSystem.Colors.foreground)
                        
                        Text("צלם או בחר תמונה של קבלה כדי לחלץ את הפרטים אוטומטית")
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.mutedForeground)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    
                    // Camera Preview or Placeholder
                    if cameraManager.isAuthorized {
                        CameraPreview(cameraManager: cameraManager)
                            .frame(height: 300)
                            .cornerRadius(DesignSystem.CornerRadius.large)
                            .overlay(
                                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                                    .stroke(DesignSystem.Colors.border, lineWidth: 2)
                            )
                            .padding(.horizontal, DesignSystem.Spacing.lg)
                    } else {
                        CameraPlaceholder {
                            cameraManager.requestPermission()
                        }
                        .frame(height: 300)
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                    }
                    
                    // Action Buttons
                    VStack(spacing: DesignSystem.Spacing.lg) {
                        // Capture Button
                        Button(action: capturePhoto) {
                            HStack {
                                if isProcessing {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primaryForeground))
                                        .scaleEffect(0.8)
                                }
                                
                                Image(systemName: "camera.fill")
                                    .font(.title2)
                                
                                Text(isProcessing ? "מעבד..." : "צלם קבלה")
                                    .font(DesignSystem.Typography.body.weight(.medium))
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: Constants.Layout.buttonHeight)
                        }
                        .primaryButton()
                        .disabled(!cameraManager.isAuthorized || isProcessing)
                        
                        // Gallery Button
                        Button(action: {
                            showingImagePicker = true
                        }) {
                            HStack {
                                Image(systemName: "photo.on.rectangle")
                                    .font(.title2)
                                
                                Text("בחר מהגלריה")
                                    .font(DesignSystem.Typography.body.weight(.medium))
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: Constants.Layout.buttonHeight)
                        }
                        .secondaryButton()
                        .disabled(isProcessing)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    
                    Spacer()
                }
                .loadingOverlay(isProcessing)
            }
            .navigationTitle("סריקת הוצאה")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                }
            }
        }
        .onAppear {
            cameraManager.requestPermission()
        }
        .sheet(isPresented: $showingImagePicker) {
            ImagePicker { image in
                processImage(image)
            }
        }
        .sheet(isPresented: $showingExpenseForm) {
            if let expense = extractedExpense {
                ExpenseFormView(extractedData: expense)
            }
        }
    }
    
    // MARK: - Methods
    
    private func capturePhoto() {
        guard cameraManager.isAuthorized else { return }
        
        isProcessing = true
        cameraManager.capturePhoto { image in
            if let image = image {
                processImage(image)
            } else {
                isProcessing = false
            }
        }
    }
    
    private func processImage(_ image: UIImage) {
        Task {
            do {
                let extractedData = try await extractExpenseData(from: image)
                await MainActor.run {
                    self.extractedExpense = extractedData
                    self.isProcessing = false
                    self.showingExpenseForm = true
                }
            } catch {
                await MainActor.run {
                    self.isProcessing = false
                    // Handle error
                }
            }
        }
    }
    
    private func extractExpenseData(from image: UIImage) async throws -> ExtractedExpenseData {
        return try await withCheckedThrowingContinuation { continuation in
            guard let cgImage = image.cgImage else {
                continuation.resume(throwing: ScanError.invalidImage)
                return
            }
            
            let request = VNRecognizeTextRequest { request, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }
                
                guard let observations = request.results as? [VNRecognizedTextObservation] else {
                    continuation.resume(throwing: ScanError.noTextFound)
                    return
                }
                
                let recognizedText = observations.compactMap { observation in
                    observation.topCandidates(1).first?.string
                }.joined(separator: "\n")
                
                let extractedData = parseExpenseData(from: recognizedText)
                continuation.resume(returning: extractedData)
            }
            
            request.recognitionLevel = .accurate
            request.recognitionLanguages = ["he", "en"]
            request.usesLanguageCorrection = true
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            do {
                try handler.perform([request])
            } catch {
                continuation.resume(throwing: error)
            }
        }
    }
    
    private func parseExpenseData(from text: String) -> ExtractedExpenseData {
        // Simple parsing logic - in production, this would be more sophisticated
        let lines = text.components(separatedBy: .newlines)
        
        var vendorName = ""
        var amount: Double = 0
        var date = Date()
        
        // Extract vendor name (usually first non-empty line)
        if let firstLine = lines.first(where: { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }) {
            vendorName = firstLine.trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        // Extract amount (look for patterns like "123.45" or "₪123.45")
        for line in lines {
            let amountPattern = #"(\d+\.?\d*)"#
            if let match = line.range(of: amountPattern, options: .regularExpression) {
                if let extractedAmount = Double(String(line[match])) {
                    amount = max(amount, extractedAmount) // Take the largest amount found
                }
            }
        }
        
        // Extract date (look for date patterns)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd/MM/yyyy"
        
        for line in lines {
            let datePattern = #"\d{1,2}/\d{1,2}/\d{4}"#
            if let match = line.range(of: datePattern, options: .regularExpression) {
                let dateString = String(line[match])
                if let extractedDate = dateFormatter.date(from: dateString) {
                    date = extractedDate
                    break
                }
            }
        }
        
        return ExtractedExpenseData(
            vendorName: vendorName.isEmpty ? "ספק לא זוהה" : vendorName,
            amount: amount,
            date: date,
            rawText: text
        )
    }
}

// MARK: - Supporting Views

struct CameraPreview: UIViewRepresentable {
    let cameraManager: CameraManager
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView(frame: UIScreen.main.bounds)
        
        if let previewLayer = cameraManager.previewLayer {
            previewLayer.frame = view.frame
            previewLayer.videoGravity = .resizeAspectFill
            view.layer.addSublayer(previewLayer)
        }
        
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        if let previewLayer = cameraManager.previewLayer {
            previewLayer.frame = uiView.bounds
        }
    }
}

struct CameraPlaceholder: View {
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: DesignSystem.Spacing.lg) {
                Image(systemName: "camera.fill")
                    .font(.system(size: 48))
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                
                Text("הרשאת מצלמה נדרשת")
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.foreground)
                
                Text("לחץ כדי לאפשר גישה למצלמה")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(DesignSystem.Colors.muted)
            .cornerRadius(DesignSystem.CornerRadius.large)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .stroke(DesignSystem.Colors.border, lineWidth: 2, lineCap: .round, dash: [10, 5])
            )
        }
    }
}

struct ImagePicker: UIViewControllerRepresentable {
    let onImageSelected: (UIImage) -> Void
    @Environment(\.dismiss) private var dismiss
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = false
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.onImageSelected(image)
            }
            parent.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

// MARK: - Camera Manager

class CameraManager: NSObject, ObservableObject {
    @Published var isAuthorized = false
    
    private let session = AVCaptureSession()
    private let photoOutput = AVCapturePhotoOutput()
    private var captureCompletion: ((UIImage?) -> Void)?
    
    lazy var previewLayer: AVCaptureVideoPreviewLayer? = {
        let layer = AVCaptureVideoPreviewLayer(session: session)
        return layer
    }()
    
    override init() {
        super.init()
        checkPermission()
    }
    
    func requestPermission() {
        AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
            DispatchQueue.main.async {
                self?.isAuthorized = granted
                if granted {
                    self?.setupCamera()
                }
            }
        }
    }
    
    private func checkPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            isAuthorized = true
            setupCamera()
        case .notDetermined:
            requestPermission()
        default:
            isAuthorized = false
        }
    }
    
    private func setupCamera() {
        guard let device = AVCaptureDevice.default(for: .video) else { return }
        
        do {
            let input = try AVCaptureDeviceInput(device: device)
            
            if session.canAddInput(input) {
                session.addInput(input)
            }
            
            if session.canAddOutput(photoOutput) {
                session.addOutput(photoOutput)
            }
            
            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                self?.session.startRunning()
            }
        } catch {
            print("Camera setup error: \(error)")
        }
    }
    
    func capturePhoto(completion: @escaping (UIImage?) -> Void) {
        captureCompletion = completion
        
        let settings = AVCapturePhotoSettings()
        photoOutput.capturePhoto(with: settings, delegate: self)
    }
}

extension CameraManager: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        guard let data = photo.fileDataRepresentation(),
              let image = UIImage(data: data) else {
            captureCompletion?(nil)
            return
        }
        
        captureCompletion?(image)
        captureCompletion = nil
    }
}

// MARK: - Data Models

struct ExtractedExpenseData {
    let vendorName: String
    let amount: Double
    let date: Date
    let rawText: String
}

enum ScanError: Error {
    case invalidImage
    case noTextFound
    case processingFailed
}

// MARK: - Expense Form View

struct ExpenseFormView: View {
    let extractedData: ExtractedExpenseData
    @Environment(\.dismiss) private var dismiss
    @State private var vendorName: String
    @State private var amount: Double
    @State private var date: Date
    @State private var category: ExpenseCategory = .other
    @State private var description: String = ""
    
    init(extractedData: ExtractedExpenseData) {
        self.extractedData = extractedData
        self._vendorName = State(initialValue: extractedData.vendorName)
        self._amount = State(initialValue: extractedData.amount)
        self._date = State(initialValue: extractedData.date)
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("פרטי ההוצאה") {
                    TextField("שם הספק", text: $vendorName)
                    TextField("סכום", value: $amount, format: .currency(code: "ILS"))
                    DatePicker("תאריך", selection: $date, displayedComponents: .date)
                }
                
                Section("קטגוריה") {
                    Picker("קטגוריה", selection: $category) {
                        ForEach(ExpenseCategory.allCases, id: \.self) { category in
                            Text(category.displayName).tag(category)
                        }
                    }
                }
                
                Section("הערות") {
                    TextField("תיאור נוסף", text: $description, axis: .vertical)
                        .lineLimit(3...6)
                }
                
                Section("טקסט שזוהה") {
                    Text(extractedData.rawText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("הוצאה חדשה")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("שמור") {
                        saveExpense()
                    }
                    .disabled(vendorName.isEmpty || amount <= 0)
                }
            }
        }
    }
    
    private func saveExpense() {
        // TODO: Save expense to database
        dismiss()
    }
}

#Preview {
    ScanExpenseView()
}
