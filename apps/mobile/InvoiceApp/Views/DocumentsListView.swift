//
//  DocumentsListView.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import SwiftUI

struct DocumentsListView: View {
    @StateObject private var viewModel = DocumentsListViewModel()
    @State private var selectedFilter: DocumentFilter = .all
    @State private var searchText = ""
    @State private var showingCreateDocument = false
    
    var body: some View {
        ZStack {
            DesignSystem.Colors.background
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Search and Filter Bar
                VStack(spacing: DesignSystem.Spacing.md) {
                    SearchBar(text: $searchText)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: DesignSystem.Spacing.md) {
                            ForEach(DocumentFilter.allCases, id: \.self) { filter in
                                FilterChip(
                                    title: filter.displayName,
                                    isSelected: selectedFilter == filter
                                ) {
                                    selectedFilter = filter
                                }
                            }
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                    }
                }
                .padding(.top, DesignSystem.Spacing.md)
                
                // Documents List
                if viewModel.isLoading {
                    Spacer()
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))
                        .scaleEffect(1.2)
                    Spacer()
                } else if filteredDocuments.isEmpty {
                    EmptyStateView(
                        icon: "doc.text",
                        title: searchText.isEmpty ? "אין מסמכים" : "לא נמצאו תוצאות",
                        subtitle: searchText.isEmpty ? "צור את המסמך הראשון שלך" : "נסה לחפש במילים אחרות",
                        actionTitle: searchText.isEmpty ? "צור מסמך" : nil
                    ) {
                        showingCreateDocument = true
                    }
                } else {
                    List {
                        ForEach(filteredDocuments, id: \.id) { document in
                            DocumentListRow(document: document)
                                .listRowBackground(Color.clear)
                                .listRowSeparator(.hidden)
                                .padding(.vertical, DesignSystem.Spacing.xs)
                        }
                    }
                    .listStyle(PlainListStyle())
                    .refreshable {
                        await viewModel.refreshDocuments()
                    }
                }
            }
        }
        .navigationTitle(Constants.HebrewText.documents)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    showingCreateDocument = true
                }) {
                    Image(systemName: "plus")
                        .foregroundColor(DesignSystem.Colors.primary)
                }
            }
        }
        .sheet(isPresented: $showingCreateDocument) {
            CreateDocumentView()
        }
        .onAppear {
            Task {
                await viewModel.loadDocuments()
            }
        }
        .onChange(of: selectedFilter) { _ in
            Task {
                await viewModel.loadDocuments(filter: selectedFilter)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredDocuments: [DocumentSummary] {
        if searchText.isEmpty {
            return viewModel.documents
        } else {
            return viewModel.documents.filter { document in
                document.documentNumber.localizedCaseInsensitiveContains(searchText) ||
                document.customerName.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
}

// MARK: - Supporting Views

struct DocumentListRow: View {
    let document: DocumentSummary
    
    var body: some View {
        NavigationLink(destination: DocumentDetailView(documentId: document.id)) {
            HStack(spacing: DesignSystem.Spacing.md) {
                // Document Type Icon
                Image(systemName: document.typeIcon)
                    .font(.title2)
                    .foregroundColor(DesignSystem.Colors.primary)
                    .frame(width: 32, height: 32)
                
                // Document Info
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    HStack {
                        Text(document.documentNumber)
                            .font(DesignSystem.Typography.body.weight(.medium))
                            .foregroundColor(DesignSystem.Colors.foreground)
                        
                        Spacer()
                        
                        StatusBadge(status: document.status)
                    }
                    
                    Text(document.customerName)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                        .lineLimit(1)
                    
                    HStack {
                        Text(document.formattedDate)
                            .font(DesignSystem.Typography.caption2)
                            .foregroundColor(DesignSystem.Colors.mutedForeground)
                        
                        Spacer()
                        
                        Text(document.formattedAmount)
                            .font(DesignSystem.Typography.caption.weight(.medium))
                            .foregroundColor(DesignSystem.Colors.primary)
                    }
                }
                
                // Chevron
                Image(systemName: "chevron.left")
                    .font(.caption)
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
            }
            .padding(DesignSystem.Spacing.md)
            .cosmicCard()
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(DesignSystem.Typography.caption.weight(.medium))
                .foregroundColor(isSelected ? DesignSystem.Colors.primaryForeground : DesignSystem.Colors.foreground)
                .padding(.horizontal, DesignSystem.Spacing.md)
                .padding(.vertical, DesignSystem.Spacing.sm)
                .background(isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.secondary)
                .cornerRadius(DesignSystem.CornerRadius.pill)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.pill)
                        .stroke(isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.border, lineWidth: 1)
                )
        }
    }
}

struct EmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    let actionTitle: String?
    let action: (() -> Void)?
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Spacer()
            
            Image(systemName: icon)
                .font(.system(size: 64))
                .foregroundColor(DesignSystem.Colors.mutedForeground)
            
            VStack(spacing: DesignSystem.Spacing.sm) {
                Text(title)
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.foreground)
                
                Text(subtitle)
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                    .multilineTextAlignment(.center)
            }
            
            if let actionTitle = actionTitle, let action = action {
                Button(action: action) {
                    Text(actionTitle)
                        .frame(maxWidth: .infinity)
                        .frame(height: Constants.Layout.buttonHeight)
                }
                .primaryButton()
                .padding(.horizontal, DesignSystem.Spacing.xxl)
            }
            
            Spacer()
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
    }
}

// MARK: - Document Detail View

struct DocumentDetailView: View {
    let documentId: String
    @StateObject private var viewModel = DocumentDetailViewModel()
    
    var body: some View {
        ZStack {
            DesignSystem.Colors.background
                .ignoresSafeArea()
            
            if viewModel.isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))
                    .scaleEffect(1.2)
            } else if let document = viewModel.document {
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.xxl) {
                        // Document Header
                        DocumentHeaderView(document: document)
                        
                        // Customer Info
                        if let customer = document.customer {
                            CustomerInfoView(customer: customer)
                        }
                        
                        // Line Items
                        if let items = document.items {
                            LineItemsView(items: items)
                        }
                        
                        // Document Summary
                        DocumentSummaryView(document: document)
                        
                        // Actions
                        DocumentActionsView(document: document)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.top, DesignSystem.Spacing.lg)
                }
            } else {
                EmptyStateView(
                    icon: "exclamationmark.triangle",
                    title: "שגיאה בטעינת המסמך",
                    subtitle: "לא ניתן לטעון את פרטי המסמך",
                    actionTitle: "נסה שוב",
                    action: {
                        Task {
                            await viewModel.loadDocument(id: documentId)
                        }
                    }
                )
            }
        }
        .navigationTitle("פרטי מסמך")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            Task {
                await viewModel.loadDocument(id: documentId)
            }
        }
    }
}

// MARK: - Document Detail Supporting Views

struct DocumentHeaderView: View {
    let document: Document
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            HStack {
                StatusBadge(status: document.status.rawValue)
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                    Text(document.documentNumber)
                        .font(DesignSystem.Typography.title3)
                        .foregroundColor(DesignSystem.Colors.foreground)
                    
                    Text(document.documentType.displayName)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                }
                
                Image(systemName: document.documentType.icon)
                    .font(.title)
                    .foregroundColor(DesignSystem.Colors.primary)
            }
            
            HStack {
                if let dueDate = document.dueDate {
                    VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                        Text("תאריך פירעון")
                            .font(DesignSystem.Typography.caption2)
                            .foregroundColor(DesignSystem.Colors.mutedForeground)
                        
                        Text(dueDate.hebrewFormatted())
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.foreground)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                    Text("תאריך הנפקה")
                        .font(DesignSystem.Typography.caption2)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                    
                    Text(document.issueDate.hebrewFormatted())
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.foreground)
                }
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
}

struct CustomerInfoView: View {
    let customer: Customer
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            Text("פרטי לקוח")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(alignment: .trailing, spacing: DesignSystem.Spacing.md) {
                InfoRow(title: "שם", value: customer.nameHebrew)
                InfoRow(title: "ח.פ", value: customer.businessNumber)
                if let vatId = customer.vatId {
                    InfoRow(title: "ע.מ", value: vatId)
                }
                InfoRow(title: "כתובת", value: "\(customer.billingAddressHebrew), \(customer.cityHebrew)")
                if let email = customer.contactEmail {
                    InfoRow(title: "אימייל", value: email)
                }
                if let phone = customer.contactPhone {
                    InfoRow(title: "טלפון", value: phone)
                }
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
}

struct LineItemsView: View {
    let items: [DocumentItem]
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            Text("פריטים")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.md) {
                ForEach(items, id: \.id) { item in
                    VStack(spacing: DesignSystem.Spacing.sm) {
                        HStack {
                            Text(item.totalWithVat.formatAsILS())
                                .font(DesignSystem.Typography.body.weight(.medium))
                                .foregroundColor(DesignSystem.Colors.primary)
                            
                            Spacer()
                            
                            Text(item.descriptionHebrew)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.foreground)
                        }
                        
                        HStack {
                            Text("מע\"מ \(item.vatRate, specifier: "%.0f")%")
                                .font(DesignSystem.Typography.caption2)
                                .foregroundColor(DesignSystem.Colors.mutedForeground)
                            
                            Spacer()
                            
                            Text("\(item.quantity, specifier: "%.0f") × \(item.unitPrice.formatAsILS())")
                                .font(DesignSystem.Typography.caption2)
                                .foregroundColor(DesignSystem.Colors.mutedForeground)
                        }
                    }
                    .padding(DesignSystem.Spacing.md)
                    .background(DesignSystem.Colors.secondary)
                    .cornerRadius(DesignSystem.CornerRadius.medium)
                }
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
}

struct DocumentSummaryView: View {
    let document: Document
    
    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            Text("סיכום")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.md) {
                SummaryRow(title: "סכום לפני מע\"מ", amount: document.subtotal, currency: document.currency)
                SummaryRow(title: "מע\"מ", amount: document.vatAmount, currency: document.currency)
                
                Divider()
                    .background(DesignSystem.Colors.border)
                
                SummaryRow(
                    title: "סה\"כ לתשלום",
                    amount: document.totalAmount,
                    currency: document.currency,
                    isTotal: true
                )
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
}

struct DocumentActionsView: View {
    let document: Document
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            if document.status == .draft {
                Button("שלח ללקוח") {
                    // TODO: Send document
                }
                .primaryButton()
            }
            
            if let pdfUrl = document.pdfUrl {
                Button("הורד PDF") {
                    // TODO: Download PDF
                }
                .secondaryButton()
            }
            
            Button("שתף") {
                // TODO: Share document
            }
            .secondaryButton()
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
    }
}

struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(value)
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            Spacer()
            
            Text(title)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.mutedForeground)
        }
    }
}

// MARK: - Filter Enum

enum DocumentFilter: CaseIterable {
    case all
    case draft
    case sent
    case paid
    case overdue
    
    var displayName: String {
        switch self {
        case .all: return "הכל"
        case .draft: return "טיוטות"
        case .sent: return "נשלחו"
        case .paid: return "שולמו"
        case .overdue: return "באיחור"
        }
    }
}

#Preview {
    NavigationView {
        DocumentsListView()
    }
}
