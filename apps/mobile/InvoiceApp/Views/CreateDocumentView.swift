//
//  CreateDocumentView.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import SwiftUI

struct CreateDocumentView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var documentType: DocumentType = .taxInvoice
    @State private var selectedCustomer: Customer?
    @State private var showingCustomerPicker = false
    @State private var issueDate = Date()
    @State private var dueDate = Date().addingTimeInterval(30 * 24 * 60 * 60) // 30 days from now
    @State private var currency = "ILS"
    @State private var notes = ""
    @State private var items: [DocumentItemInput] = [DocumentItemInput()]
    
    var body: some View {
        NavigationView {
            ZStack {
                DesignSystem.Colors.background
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.xxl) {
                        // Document Type Selection
                        documentTypeSection
                        
                        // Customer Selection
                        customerSection
                        
                        // Document Details
                        documentDetailsSection
                        
                        // Line Items
                        lineItemsSection
                        
                        // Summary
                        summarySection
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.top, DesignSystem.Spacing.lg)
                }
            }
            .navigationTitle("מסמך חדש")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("שמור") {
                        saveDocument()
                    }
                    .foregroundColor(DesignSystem.Colors.primary)
                    .disabled(!isFormValid)
                }
            }
        }
        .sheet(isPresented: $showingCustomerPicker) {
            CustomerPickerView(selectedCustomer: $selectedCustomer)
        }
    }
    
    // MARK: - Document Type Section
    
    private var documentTypeSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            Text("סוג מסמך")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            HStack(spacing: DesignSystem.Spacing.md) {
                ForEach(DocumentType.allCases, id: \.self) { type in
                    Button(action: {
                        documentType = type
                    }) {
                        VStack(spacing: DesignSystem.Spacing.sm) {
                            Image(systemName: type.icon)
                                .font(.title2)
                                .foregroundColor(documentType == type ? DesignSystem.Colors.primaryForeground : DesignSystem.Colors.foreground)
                            
                            Text(type.displayName)
                                .font(DesignSystem.Typography.caption)
                                .foregroundColor(documentType == type ? DesignSystem.Colors.primaryForeground : DesignSystem.Colors.foreground)
                                .multilineTextAlignment(.center)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(DesignSystem.Spacing.md)
                        .background(documentType == type ? DesignSystem.Colors.primary : DesignSystem.Colors.secondary)
                        .cornerRadius(DesignSystem.CornerRadius.medium)
                    }
                }
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
    
    // MARK: - Customer Section
    
    private var customerSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            Text("לקוח")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            Button(action: {
                showingCustomerPicker = true
            }) {
                HStack {
                    Image(systemName: "chevron.left")
                        .font(.caption)
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                    
                    Spacer()
                    
                    if let customer = selectedCustomer {
                        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                            Text(customer.nameHebrew)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.foreground)
                            
                            Text(customer.businessNumber)
                                .font(DesignSystem.Typography.caption)
                                .foregroundColor(DesignSystem.Colors.mutedForeground)
                        }
                    } else {
                        Text("בחר לקוח")
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.mutedForeground)
                    }
                    
                    Image(systemName: "person.circle")
                        .font(.title2)
                        .foregroundColor(DesignSystem.Colors.primary)
                }
                .padding(DesignSystem.Spacing.md)
                .background(DesignSystem.Colors.secondary)
                .cornerRadius(DesignSystem.CornerRadius.medium)
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
    
    // MARK: - Document Details Section
    
    private var documentDetailsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            Text("פרטי המסמך")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.lg) {
                // Issue Date
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm) {
                    Text("תאריך הנפקה")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.foreground)
                    
                    DatePicker("", selection: $issueDate, displayedComponents: .date)
                        .datePickerStyle(CompactDatePickerStyle())
                        .environment(\.locale, Locale(identifier: "he_IL"))
                }
                
                // Due Date (for invoices)
                if documentType == .taxInvoice || documentType == .taxInvoiceReceipt {
                    VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm) {
                        Text("תאריך פירעון")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.foreground)
                        
                        DatePicker("", selection: $dueDate, displayedComponents: .date)
                            .datePickerStyle(CompactDatePickerStyle())
                            .environment(\.locale, Locale(identifier: "he_IL"))
                    }
                }
                
                // Currency
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm) {
                    Text("מטבע")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.foreground)
                    
                    Picker("מטבע", selection: $currency) {
                        Text("שקל ישראלי (₪)").tag("ILS")
                        Text("דולר אמריקני ($)").tag("USD")
                        Text("יורו (€)").tag("EUR")
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                // Notes
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm) {
                    Text("הערות")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.foreground)
                    
                    TextField("הערות נוספות", text: $notes, axis: .vertical)
                        .textFieldStyle(CosmicTextFieldStyle())
                        .lineLimit(3...6)
                }
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
    
    // MARK: - Line Items Section
    
    private var lineItemsSection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            HStack {
                Button(action: addLineItem) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(DesignSystem.Colors.primary)
                }
                
                Spacer()
                
                Text("פריטים")
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.foreground)
            }
            
            VStack(spacing: DesignSystem.Spacing.md) {
                ForEach(items.indices, id: \.self) { index in
                    LineItemRow(
                        item: $items[index],
                        currency: currency,
                        onDelete: {
                            if items.count > 1 {
                                items.remove(at: index)
                            }
                        }
                    )
                }
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
    
    // MARK: - Summary Section
    
    private var summarySection: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.lg) {
            Text("סיכום")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.md) {
                SummaryRow(title: "סכום לפני מע\"מ", amount: subtotal, currency: currency)
                SummaryRow(title: "מע\"מ", amount: vatAmount, currency: currency)
                
                Divider()
                    .background(DesignSystem.Colors.border)
                
                SummaryRow(
                    title: "סה\"כ לתשלום",
                    amount: totalAmount,
                    currency: currency,
                    isTotal: true
                )
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .cosmicCard()
    }
    
    // MARK: - Computed Properties
    
    private var subtotal: Double {
        items.reduce(0) { $0 + $1.lineTotal }
    }
    
    private var vatAmount: Double {
        items.reduce(0) { $0 + $1.vatAmount }
    }
    
    private var totalAmount: Double {
        subtotal + vatAmount
    }
    
    private var isFormValid: Bool {
        selectedCustomer != nil && !items.isEmpty && items.allSatisfy { !$0.description.isEmpty && $0.quantity > 0 && $0.unitPrice > 0 }
    }
    
    // MARK: - Methods
    
    private func addLineItem() {
        items.append(DocumentItemInput())
    }
    
    private func saveDocument() {
        // TODO: Implement document saving
        dismiss()
    }
}

// MARK: - Supporting Models

struct DocumentItemInput {
    var description: String = ""
    var quantity: Double = 1.0
    var unitPrice: Double = 0.0
    var vatRate: Double = 17.0 // Default Israeli VAT rate
    
    var lineTotal: Double {
        quantity * unitPrice
    }
    
    var vatAmount: Double {
        lineTotal * (vatRate / 100)
    }
    
    var totalWithVat: Double {
        lineTotal + vatAmount
    }
}

// MARK: - Supporting Views

struct LineItemRow: View {
    @Binding var item: DocumentItemInput
    let currency: String
    let onDelete: () -> Void
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            HStack {
                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .foregroundColor(DesignSystem.Colors.destructive)
                }
                
                Spacer()
                
                TextField("תיאור הפריט", text: $item.description)
                    .textFieldStyle(CosmicTextFieldStyle())
            }
            
            HStack(spacing: DesignSystem.Spacing.md) {
                TextField("מע\"מ %", value: $item.vatRate, format: .number)
                    .textFieldStyle(CosmicTextFieldStyle())
                    .keyboardType(.decimalPad)
                
                TextField("מחיר יחידה", value: $item.unitPrice, format: .number)
                    .textFieldStyle(CosmicTextFieldStyle())
                    .keyboardType(.decimalPad)
                
                TextField("כמות", value: $item.quantity, format: .number)
                    .textFieldStyle(CosmicTextFieldStyle())
                    .keyboardType(.decimalPad)
            }
            
            HStack {
                Spacer()
                Text("סה\"כ: \(item.totalWithVat.formatAsILS())")
                    .font(DesignSystem.Typography.caption.weight(.medium))
                    .foregroundColor(DesignSystem.Colors.primary)
            }
        }
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.secondary)
        .cornerRadius(DesignSystem.CornerRadius.medium)
    }
}

struct SummaryRow: View {
    let title: String
    let amount: Double
    let currency: String
    var isTotal: Bool = false
    
    var body: some View {
        HStack {
            Text(amount.formatAsILS())
                .font(isTotal ? DesignSystem.Typography.headline : DesignSystem.Typography.body)
                .foregroundColor(isTotal ? DesignSystem.Colors.primary : DesignSystem.Colors.foreground)
                .fontWeight(isTotal ? .bold : .regular)
            
            Spacer()
            
            Text(title)
                .font(isTotal ? DesignSystem.Typography.headline : DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.foreground)
                .fontWeight(isTotal ? .bold : .regular)
        }
    }
}

struct CustomerPickerView: View {
    @Binding var selectedCustomer: Customer?
    @Environment(\.dismiss) private var dismiss
    @State private var customers: [Customer] = []
    @State private var searchText = ""
    
    var body: some View {
        NavigationView {
            VStack {
                SearchBar(text: $searchText)
                
                List(filteredCustomers, id: \.id) { customer in
                    Button(action: {
                        selectedCustomer = customer
                        dismiss()
                    }) {
                        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.xs) {
                            Text(customer.nameHebrew)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.foreground)
                            
                            Text(customer.businessNumber)
                                .font(DesignSystem.Typography.caption)
                                .foregroundColor(DesignSystem.Colors.mutedForeground)
                        }
                    }
                }
            }
            .navigationTitle("בחר לקוח")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("ביטול") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            loadCustomers()
        }
    }
    
    private var filteredCustomers: [Customer] {
        if searchText.isEmpty {
            return customers
        } else {
            return customers.filter { customer in
                customer.nameHebrew.localizedCaseInsensitiveContains(searchText) ||
                customer.businessNumber.contains(searchText)
            }
        }
    }
    
    private func loadCustomers() {
        // TODO: Load customers from API
        // For now, using mock data
        customers = []
    }
}

struct SearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(DesignSystem.Colors.mutedForeground)
            
            TextField("חפש לקוח...", text: $text)
                .textFieldStyle(PlainTextFieldStyle())
        }
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.input)
        .cornerRadius(DesignSystem.CornerRadius.medium)
        .padding(.horizontal, DesignSystem.Spacing.lg)
    }
}

#Preview {
    CreateDocumentView()
}
