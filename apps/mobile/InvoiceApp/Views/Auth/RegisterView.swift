//
//  RegisterView.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import SwiftUI

struct RegisterView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var formData = RegistrationFormData()
    @State private var currentStep = 1
    @FocusState private var focusedField: Field?
    
    private let totalSteps = 6
    
    enum Field {
        case companyName, annualRevenue, fullName, email, password, confirmPassword, phone
        case businessNumber, companyNameHebrew, companyNameEnglish, vatId, address, city, companyPhone
    }
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    DesignSystem.Colors.background
                        .ignoresSafeArea()
                    
                    CosmicGridBackground()
                    
                    VStack(spacing: 0) {
                        // Header
                        VStack(spacing: DesignSystem.Spacing.lg) {
                            Text(Constants.HebrewText.register)
                                .font(DesignSystem.Typography.title2)
                                .foregroundColor(DesignSystem.Colors.foreground)
                            
                            Text("צור חשבון חדש למערכת החשבוניות")
                                .font(DesignSystem.Typography.caption)
                                .foregroundColor(DesignSystem.Colors.mutedForeground)
                                .multilineTextAlignment(.center)
                            
                            // Progress Bar
                            ProgressView(value: Double(currentStep), total: Double(totalSteps))
                                .progressViewStyle(LinearProgressViewStyle(tint: DesignSystem.Colors.primary))
                                .background(DesignSystem.Colors.muted)
                                .cornerRadius(DesignSystem.CornerRadius.small)
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        .padding(.top, DesignSystem.Spacing.lg)
                        
                        // Step Content
                        ScrollView {
                            VStack(spacing: DesignSystem.Spacing.xxl) {
                                stepContent
                                    .padding(.horizontal, DesignSystem.Spacing.lg)
                                
                                // Navigation Buttons
                                HStack(spacing: DesignSystem.Spacing.lg) {
                                    if currentStep > 1 {
                                        Button(action: previousStep) {
                                            Text(Constants.HebrewText.previous)
                                                .frame(maxWidth: .infinity)
                                                .frame(height: Constants.Layout.buttonHeight)
                                        }
                                        .secondaryButton()
                                    }
                                    
                                    Button(action: {
                                        Task {
                                            await nextStep()
                                        }
                                    }) {
                                        HStack {
                                            if authViewModel.isLoading {
                                                ProgressView()
                                                    .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primaryForeground))
                                                    .scaleEffect(0.8)
                                            }
                                            
                                            Text(currentStep == totalSteps ? Constants.HebrewText.submit : Constants.HebrewText.next)
                                                .frame(maxWidth: .infinity)
                                                .frame(height: Constants.Layout.buttonHeight)
                                        }
                                    }
                                    .primaryButton()
                                    .disabled(!isCurrentStepValid || authViewModel.isLoading)
                                    .opacity(isCurrentStepValid ? 1.0 : 0.6)
                                }
                                .padding(.horizontal, DesignSystem.Spacing.lg)
                                .padding(.bottom, DesignSystem.Spacing.xxl)
                            }
                        }
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                }
            }
        }
        .alert("שגיאה", isPresented: .constant(authViewModel.errorMessage != nil)) {
            Button("אישור") {
                authViewModel.clearError()
            }
        } message: {
            if let errorMessage = authViewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .onTapGesture {
            hideKeyboard()
        }
    }
    
    // MARK: - Step Content
    
    @ViewBuilder
    private var stepContent: some View {
        VStack(spacing: DesignSystem.Spacing.xxl) {
            switch currentStep {
            case 1:
                surveyStep1
            case 2:
                surveyStep2
            case 3:
                accountStep
            case 4:
                companyStep1
            case 5:
                companyStep2
            case 6:
                surveyStep3
            default:
                EmptyView()
            }
        }
        .cosmicGlass()
        .padding(.vertical, DesignSystem.Spacing.xxl)
    }
    
    // Step 1: Company Name
    private var surveyStep1: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Text("שם החברה")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            TextField("הכנס שם החברה", text: $formData.companyNameHebrew)
                .textFieldStyle(CosmicTextFieldStyle())
                .focused($focusedField, equals: .companyNameHebrew)
        }
    }
    
    // Step 2: Annual Revenue
    private var surveyStep2: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Text("מחזור שנתי")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.md) {
                ForEach(Constants.annualRevenueOptions, id: \.self) { option in
                    Button(action: {
                        formData.annualRevenue = option
                    }) {
                        HStack {
                            Spacer()
                            Text(option)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(formData.annualRevenue == option ? DesignSystem.Colors.primaryForeground : DesignSystem.Colors.foreground)
                            Spacer()
                        }
                        .padding(DesignSystem.Spacing.md)
                        .background(formData.annualRevenue == option ? DesignSystem.Colors.primary : DesignSystem.Colors.secondary)
                        .cornerRadius(DesignSystem.CornerRadius.medium)
                        .overlay(
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                                .stroke(formData.annualRevenue == option ? DesignSystem.Colors.primary : DesignSystem.Colors.border, lineWidth: 1)
                        )
                    }
                }
            }
        }
    }
    
    // Step 3: Account Information
    private var accountStep: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Text("פרטי החשבון")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.lg) {
                FormField(title: "שם מלא", text: $formData.fullName, field: .fullName, focusedField: $focusedField)
                FormField(title: "אימייל", text: $formData.email, field: .email, focusedField: $focusedField, keyboardType: .emailAddress)
                FormField(title: "טלפון", text: $formData.phone, field: .phone, focusedField: $focusedField, keyboardType: .phonePad)
                SecureFormField(title: "סיסמה", text: $formData.password, field: .password, focusedField: $focusedField)
                SecureFormField(title: "אישור סיסמה", text: $formData.confirmPassword, field: .confirmPassword, focusedField: $focusedField)
            }
        }
    }
    
    // Step 4: Company Information Part 1
    private var companyStep1: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Text("פרטי החברה")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.lg) {
                FormField(title: "ח.פ / ע.מ", text: $formData.businessNumber, field: .businessNumber, focusedField: $focusedField, keyboardType: .numberPad)
                FormField(title: "שם החברה באנגלית", text: $formData.companyNameEnglish, field: .companyNameEnglish, focusedField: $focusedField)
                FormField(title: "מספר עוסק מורשה", text: $formData.vatId, field: .vatId, focusedField: $focusedField, keyboardType: .numberPad)
            }
        }
    }
    
    // Step 5: Company Information Part 2
    private var companyStep2: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Text("כתובת החברה")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.lg) {
                FormField(title: "כתובת", text: $formData.addressHebrew, field: .address, focusedField: $focusedField)
                FormField(title: "עיר", text: $formData.cityHebrew, field: .city, focusedField: $focusedField)
                FormField(title: "טלפון החברה", text: $formData.companyPhone, field: .companyPhone, focusedField: $focusedField, keyboardType: .phonePad)
                
                // Industry Picker
                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm) {
                    Text("תחום עיסוק")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.foreground)
                    
                    Menu {
                        ForEach(Constants.industryOptions, id: \.self) { industry in
                            Button(industry) {
                                formData.industry = industry
                            }
                        }
                    } label: {
                        HStack {
                            Image(systemName: "chevron.down")
                                .foregroundColor(DesignSystem.Colors.mutedForeground)
                            
                            Spacer()
                            
                            Text(formData.industry.isEmpty ? "בחר תחום עיסוק" : formData.industry)
                                .foregroundColor(formData.industry.isEmpty ? DesignSystem.Colors.mutedForeground : DesignSystem.Colors.foreground)
                        }
                        .padding(DesignSystem.Spacing.md)
                        .background(DesignSystem.Colors.input)
                        .cornerRadius(DesignSystem.CornerRadius.medium)
                        .overlay(
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                                .stroke(DesignSystem.Colors.border, lineWidth: 1)
                        )
                    }
                }
            }
        }
    }
    
    // Step 6: Survey Questions
    private var surveyStep3: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Text("שאלות נוספות")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.foreground)
            
            VStack(spacing: DesignSystem.Spacing.lg) {
                SurveyQuestion(
                    question: "מעוניין בהלוואה עסקית?",
                    isSelected: $formData.interestedInLoan
                )
                
                SurveyQuestion(
                    question: "מעוניין בביטוח עסקי?",
                    isSelected: $formData.interestedInInsurance
                )
                
                SurveyQuestion(
                    question: "מעוניין בשירותי הנהלת חשבונות?",
                    isSelected: $formData.interestedInAccounting
                )
            }
        }
    }

    // MARK: - Computed Properties

    private var isCurrentStepValid: Bool {
        switch currentStep {
        case 1:
            return !formData.companyNameHebrew.isEmpty
        case 2:
            return !formData.annualRevenue.isEmpty
        case 3:
            return !formData.fullName.isEmpty &&
                   !formData.email.isEmpty &&
                   !formData.phone.isEmpty &&
                   !formData.password.isEmpty &&
                   !formData.confirmPassword.isEmpty &&
                   authViewModel.validateEmail(formData.email) &&
                   authViewModel.validatePassword(formData.password) &&
                   formData.password == formData.confirmPassword
        case 4:
            return !formData.businessNumber.isEmpty &&
                   !formData.vatId.isEmpty &&
                   authViewModel.validateBusinessNumber(formData.businessNumber) &&
                   authViewModel.validateVatId(formData.vatId)
        case 5:
            return !formData.addressHebrew.isEmpty &&
                   !formData.cityHebrew.isEmpty &&
                   !formData.companyPhone.isEmpty &&
                   !formData.industry.isEmpty
        case 6:
            return true // Survey questions are optional
        default:
            return false
        }
    }

    // MARK: - Methods

    private func previousStep() {
        withAnimation(DesignSystem.Animation.standard) {
            currentStep = max(1, currentStep - 1)
        }
        hideKeyboard()
    }

    private func nextStep() async {
        hideKeyboard()

        if currentStep < totalSteps {
            withAnimation(DesignSystem.Animation.standard) {
                currentStep += 1
            }
        } else {
            // Submit registration
            await authViewModel.completeRegistration(registrationData: formData.toRegistrationData())

            if authViewModel.isAuthenticated {
                dismiss()
            }
        }
    }

    private func hideKeyboard() {
        focusedField = nil
    }
}

// MARK: - Supporting Views

struct FormField: View {
    let title: String
    @Binding var text: String
    let field: RegisterView.Field
    @FocusState.Binding var focusedField: RegisterView.Field?
    var keyboardType: UIKeyboardType = .default
    var placeholder: String = ""

    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm) {
            Text(title)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.foreground)

            TextField(placeholder.isEmpty ? title : placeholder, text: $text)
                .textFieldStyle(CosmicTextFieldStyle())
                .keyboardType(keyboardType)
                .focused($focusedField, equals: field)
                .autocapitalization(keyboardType == .emailAddress ? .none : .words)
                .disableAutocorrection(keyboardType == .emailAddress)
        }
    }
}

struct SecureFormField: View {
    let title: String
    @Binding var text: String
    let field: RegisterView.Field
    @FocusState.Binding var focusedField: RegisterView.Field?
    @State private var showPassword = false

    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm) {
            Text(title)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.foreground)

            HStack {
                Button(action: {
                    showPassword.toggle()
                }) {
                    Image(systemName: showPassword ? "eye.slash" : "eye")
                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                }

                if showPassword {
                    TextField(title, text: $text)
                        .focused($focusedField, equals: field)
                } else {
                    SecureField(title, text: $text)
                        .focused($focusedField, equals: field)
                }
            }
            .padding(DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.input)
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(DesignSystem.Colors.border, lineWidth: 1)
            )
        }
    }
}

struct SurveyQuestion: View {
    let question: String
    @Binding var isSelected: Bool

    var body: some View {
        VStack(alignment: .trailing, spacing: DesignSystem.Spacing.md) {
            Text(question)
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.foreground)
                .multilineTextAlignment(.trailing)

            HStack(spacing: DesignSystem.Spacing.lg) {
                // No Button
                Button(action: {
                    isSelected = false
                }) {
                    HStack {
                        Spacer()
                        Text(Constants.HebrewText.no)
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(!isSelected ? DesignSystem.Colors.primaryForeground : DesignSystem.Colors.foreground)
                        Spacer()
                    }
                    .padding(DesignSystem.Spacing.md)
                    .background(!isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.secondary)
                    .cornerRadius(DesignSystem.CornerRadius.medium)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                            .stroke(!isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.border, lineWidth: 1)
                    )
                }

                // Yes Button
                Button(action: {
                    isSelected = true
                }) {
                    HStack {
                        Spacer()
                        Text(Constants.HebrewText.yes)
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(isSelected ? DesignSystem.Colors.primaryForeground : DesignSystem.Colors.foreground)
                        Spacer()
                    }
                    .padding(DesignSystem.Spacing.md)
                    .background(isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.secondary)
                    .cornerRadius(DesignSystem.CornerRadius.medium)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                            .stroke(isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.border, lineWidth: 1)
                    )
                }
            }
        }
    }
}

#Preview {
    RegisterView()
        .environmentObject(AuthViewModel())
}
