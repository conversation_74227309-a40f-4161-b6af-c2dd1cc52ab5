//
//  LoginView.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import SwiftUI

struct LoginView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var email = ""
    @State private var password = ""
    @State private var showPassword = false
    @State private var showRegister = false
    @FocusState private var focusedField: Field?
    
    enum Field {
        case email, password
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background with cosmic grid
                DesignSystem.Colors.background
                    .ignoresSafeArea()
                
                // Cosmic grid pattern
                CosmicGridBackground()
                
                ScrollView {
                    VStack(spacing: 0) {
                        Spacer()
                            .frame(height: geometry.size.height * 0.1)
                        
                        // Login Card
                        VStack(spacing: DesignSystem.Spacing.xxl) {
                            // Header
                            VStack(spacing: DesignSystem.Spacing.md) {
                                Text(Constants.HebrewText.login)
                                    .font(DesignSystem.Typography.title2)
                                    .foregroundColor(DesignSystem.Colors.foreground)
                                    .multilineTextAlignment(.center)
                                
                                Text("הכנס את פרטי החשבון שלך כדי להתחבר")
                                    .font(DesignSystem.Typography.caption)
                                    .foregroundColor(DesignSystem.Colors.mutedForeground)
                                    .multilineTextAlignment(.center)
                            }
                            
                            // Login Form
                            VStack(spacing: DesignSystem.Spacing.lg) {
                                // Email Field
                                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm) {
                                    HStack {
                                        Spacer()
                                        Text(Constants.HebrewText.email)
                                            .font(DesignSystem.Typography.caption)
                                            .foregroundColor(DesignSystem.Colors.foreground)
                                    }
                                    
                                    TextField("<EMAIL>", text: $email)
                                        .textFieldStyle(CosmicTextFieldStyle())
                                        .keyboardType(.emailAddress)
                                        .autocapitalization(.none)
                                        .disableAutocorrection(true)
                                        .focused($focusedField, equals: .email)
                                        .submitLabel(.next)
                                        .onSubmit {
                                            focusedField = .password
                                        }
                                }
                                
                                // Password Field
                                VStack(alignment: .trailing, spacing: DesignSystem.Spacing.sm) {
                                    HStack {
                                        Spacer()
                                        Text(Constants.HebrewText.password)
                                            .font(DesignSystem.Typography.caption)
                                            .foregroundColor(DesignSystem.Colors.foreground)
                                    }
                                    
                                    HStack {
                                        Button(action: {
                                            showPassword.toggle()
                                        }) {
                                            Image(systemName: showPassword ? "eye.slash" : "eye")
                                                .foregroundColor(DesignSystem.Colors.mutedForeground)
                                        }
                                        
                                        if showPassword {
                                            TextField("••••••••", text: $password)
                                                .focused($focusedField, equals: .password)
                                        } else {
                                            SecureField("••••••••", text: $password)
                                                .focused($focusedField, equals: .password)
                                        }
                                    }
                                    .padding(DesignSystem.Spacing.md)
                                    .background(DesignSystem.Colors.input)
                                    .cornerRadius(DesignSystem.CornerRadius.medium)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                                            .stroke(DesignSystem.Colors.border, lineWidth: 1)
                                    )
                                    .submitLabel(.go)
                                    .onSubmit {
                                        Task {
                                            await handleLogin()
                                        }
                                    }
                                }
                                
                                // Login Button
                                Button(action: {
                                    Task {
                                        await handleLogin()
                                    }
                                }) {
                                    HStack {
                                        if authViewModel.isLoading {
                                            ProgressView()
                                                .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primaryForeground))
                                                .scaleEffect(0.8)
                                        }
                                        
                                        Text(authViewModel.isLoading ? "מתחבר..." : Constants.HebrewText.loginButton)
                                            .font(DesignSystem.Typography.body.weight(.medium))
                                    }
                                    .frame(maxWidth: .infinity)
                                    .frame(height: Constants.Layout.buttonHeight)
                                    .background(DesignSystem.Colors.primary)
                                    .foregroundColor(DesignSystem.Colors.primaryForeground)
                                    .cornerRadius(DesignSystem.CornerRadius.medium)
                                }
                                .disabled(authViewModel.isLoading || !isFormValid)
                                .opacity(isFormValid ? 1.0 : 0.6)
                            }
                            
                            // Register Link
                            VStack(spacing: DesignSystem.Spacing.md) {
                                HStack(spacing: DesignSystem.Spacing.sm) {
                                    Button(action: {
                                        showRegister = true
                                    }) {
                                        Text(Constants.HebrewText.signUpHere)
                                            .font(DesignSystem.Typography.caption.weight(.medium))
                                            .foregroundColor(DesignSystem.Colors.primary)
                                    }
                                    
                                    Text(Constants.HebrewText.noAccount)
                                        .font(DesignSystem.Typography.caption)
                                        .foregroundColor(DesignSystem.Colors.mutedForeground)
                                }
                            }
                        }
                        .padding(DesignSystem.Spacing.xxl)
                        .cosmicGlass()
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        
                        Spacer()
                            .frame(height: geometry.size.height * 0.1)
                    }
                }
            }
        }
        .alert("שגיאה", isPresented: .constant(authViewModel.errorMessage != nil)) {
            Button("אישור") {
                authViewModel.clearError()
            }
        } message: {
            if let errorMessage = authViewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .sheet(isPresented: $showRegister) {
            RegisterView()
        }
        .onTapGesture {
            hideKeyboard()
        }
    }
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !email.isEmpty && !password.isEmpty && authViewModel.validateEmail(email)
    }
    
    // MARK: - Methods
    
    private func handleLogin() async {
        hideKeyboard()
        await authViewModel.signIn(email: email, password: password)
    }
    
    private func hideKeyboard() {
        focusedField = nil
    }
}

// MARK: - Custom Text Field Style

struct CosmicTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.input)
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(DesignSystem.Colors.border, lineWidth: 1)
            )
            .font(DesignSystem.Typography.body)
            .foregroundColor(DesignSystem.Colors.foreground)
            .multilineTextAlignment(.trailing)
    }
}

// MARK: - Cosmic Grid Background

struct CosmicGridBackground: View {
    var body: some View {
        Canvas { context, size in
            let spacing: CGFloat = 30
            
            context.stroke(
                Path { path in
                    // Vertical lines
                    for x in stride(from: 0, through: size.width, by: spacing) {
                        path.move(to: CGPoint(x: x, y: 0))
                        path.addLine(to: CGPoint(x: x, y: size.height))
                    }
                    
                    // Horizontal lines
                    for y in stride(from: 0, through: size.height, by: spacing) {
                        path.move(to: CGPoint(x: 0, y: y))
                        path.addLine(to: CGPoint(x: size.width, y: y))
                    }
                },
                with: .color(Color.white.opacity(0.05)),
                lineWidth: 1
            )
        }
        .ignoresSafeArea()
    }
}

#Preview {
    LoginView()
        .environmentObject(AuthViewModel())
}
