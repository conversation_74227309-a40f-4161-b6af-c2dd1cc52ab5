//
//  DocumentsListViewModel.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import Foundation
import SwiftUI

@MainActor
class DocumentsListViewModel: ObservableObject {
    @Published var documents: [DocumentSummary] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    
    func loadDocuments(filter: DocumentFilter = .all) async {
        isLoading = true
        errorMessage = nil
        
        do {
            // In a real implementation, this would call the Supabase API
            // For now, we'll use mock data
            let mockDocuments = generateMockDocuments()
            
            // Apply filter
            switch filter {
            case .all:
                documents = mockDocuments
            case .draft:
                documents = mockDocuments.filter { $0.status == "draft" }
            case .sent:
                documents = mockDocuments.filter { $0.status == "sent" }
            case .paid:
                documents = mockDocuments.filter { $0.status == "paid" }
            case .overdue:
                documents = mockDocuments.filter { 
                    $0.status == "sent" && Calendar.current.dateInterval(of: .day, for: $0.issueDate)?.end ?? Date() < Date()
                }
            }
            
            // Simulate network delay
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    func refreshDocuments() async {
        await loadDocuments()
    }
    
    private func generateMockDocuments() -> [DocumentSummary] {
        return [
            DocumentSummary(
                id: "1",
                documentNumber: "INV-2025-001",
                documentType: "tax_invoice",
                customerName: "חברת טכנולוגיה בע\"מ",
                amount: 5000.0,
                currency: "ILS",
                status: "sent",
                issueDate: Date().addingTimeInterval(-2 * 24 * 60 * 60)
            ),
            DocumentSummary(
                id: "2",
                documentNumber: "INV-2025-002",
                documentType: "tax_invoice",
                customerName: "סטארט-אפ חדשני בע\"מ",
                amount: 8500.0,
                currency: "ILS",
                status: "draft",
                issueDate: Date().addingTimeInterval(-1 * 24 * 60 * 60)
            ),
            DocumentSummary(
                id: "3",
                documentNumber: "REC-2025-001",
                documentType: "receipt",
                customerName: "לקוח פרטי",
                amount: 1200.0,
                currency: "ILS",
                status: "paid",
                issueDate: Date()
            ),
            DocumentSummary(
                id: "4",
                documentNumber: "INV-2025-003",
                documentType: "tax_invoice",
                customerName: "חברת ייעוץ בע\"מ",
                amount: 3500.0,
                currency: "ILS",
                status: "sent",
                issueDate: Date().addingTimeInterval(-5 * 24 * 60 * 60)
            ),
            DocumentSummary(
                id: "5",
                documentNumber: "CR-2025-001",
                documentType: "credit_note",
                customerName: "חברת טכנולוגיה בע\"מ",
                amount: -500.0,
                currency: "ILS",
                status: "sent",
                issueDate: Date().addingTimeInterval(-3 * 24 * 60 * 60)
            )
        ]
    }
}

@MainActor
class DocumentDetailViewModel: ObservableObject {
    @Published var document: Document?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    
    func loadDocument(id: String) async {
        isLoading = true
        errorMessage = nil
        
        do {
            // In a real implementation, this would call the Supabase API
            // For now, we'll use mock data
            document = generateMockDocument(id: id)
            
            // Simulate network delay
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    private func generateMockDocument(id: String) -> Document {
        let mockCustomer = Customer(
            id: "customer-1",
            companyId: "company-1",
            businessNumber: "*********",
            nameHebrew: "חברת טכנולוגיה בע\"מ",
            nameEnglish: "Technology Company Ltd",
            vatId: "*********",
            billingAddressHebrew: "רחוב הטכנולוגיה 123",
            billingAddressEnglish: "123 Technology Street",
            shippingAddressHebrew: nil,
            shippingAddressEnglish: nil,
            cityHebrew: "תל אביב",
            cityEnglish: "Tel Aviv",
            contactName: "יוסי כהן",
            contactEmail: "<EMAIL>",
            contactPhone: "03-1234567",
            notes: nil,
            createdAt: Date(),
            updatedAt: Date()
        )
        
        let mockItems = [
            DocumentItem(
                id: "item-1",
                documentId: id,
                productId: nil,
                lineNumber: 1,
                descriptionHebrew: "פיתוח אפליקציה",
                descriptionEnglish: "App Development",
                quantity: 1.0,
                unitPrice: 5000.0,
                currency: "ILS",
                discountPercent: 0.0,
                vatRate: 17.0,
                lineTotal: 5000.0,
                vatAmount: 850.0,
                totalWithVat: 5850.0
            ),
            DocumentItem(
                id: "item-2",
                documentId: id,
                productId: nil,
                lineNumber: 2,
                descriptionHebrew: "ייעוץ טכני",
                descriptionEnglish: "Technical Consulting",
                quantity: 10.0,
                unitPrice: 300.0,
                currency: "ILS",
                discountPercent: 0.0,
                vatRate: 17.0,
                lineTotal: 3000.0,
                vatAmount: 510.0,
                totalWithVat: 3510.0
            )
        ]
        
        return Document(
            id: id,
            companyId: "company-1",
            documentType: .taxInvoice,
            documentNumber: "INV-2025-001",
            customerId: "customer-1",
            issueDate: Date().addingTimeInterval(-2 * 24 * 60 * 60),
            dueDate: Date().addingTimeInterval(28 * 24 * 60 * 60),
            currency: "ILS",
            subtotal: 8000.0,
            vatAmount: 1360.0,
            totalAmount: 9360.0,
            status: .sent,
            itaAllocationNumber: nil,
            itaAllocationDate: nil,
            itaSubmissionAttempts: 0,
            itaLastError: nil,
            parentDocumentId: nil,
            notes: "תשלום תוך 30 יום",
            templateId: "template-1",
            pdfUrl: "https://example.com/invoice.pdf",
            sentAt: Date().addingTimeInterval(-1 * 24 * 60 * 60),
            sentVia: "email",
            createdBy: "user-1",
            createdAt: Date().addingTimeInterval(-2 * 24 * 60 * 60),
            updatedAt: Date().addingTimeInterval(-1 * 24 * 60 * 60),
            customer: mockCustomer,
            items: mockItems
        )
    }
}
