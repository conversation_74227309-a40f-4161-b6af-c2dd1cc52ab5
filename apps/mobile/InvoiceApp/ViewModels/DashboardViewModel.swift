//
//  DashboardViewModel.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import Foundation
import SwiftUI

@MainActor
class DashboardViewModel: ObservableObject {
    @Published var monthlyRevenue: String = "₪0"
    @Published var revenueTrend: String = ""
    @Published var vatLiability: String = "₪0"
    @Published var nextPaymentDate: String = ""
    @Published var openInvoicesCount: Int = 0
    @Published var openInvoicesAmount: String = "₪0"
    @Published var pendingExpensesCount: Int = 0
    @Published var recentDocuments: [DocumentSummary] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    
    func loadData() async {
        isLoading = true
        errorMessage = nil
        
        do {
            async let statsTask = loadDashboardStats()
            async let documentsTask = loadRecentDocuments()
            async let expensesTask = loadPendingExpenses()
            
            let (stats, documents, expensesCount) = try await (statsTask, documentsTask, expensesTask)
            
            updateStats(stats)
            recentDocuments = documents
            pendingExpensesCount = expensesCount
            
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    func refreshData() async {
        await loadData()
    }
    
    // MARK: - Private Methods
    
    private func loadDashboardStats() async throws -> DashboardStats {
        // This would typically call a Supabase function or query multiple tables
        // For now, returning mock data
        return DashboardStats(
            monthlyRevenue: 45000.0,
            previousMonthRevenue: 38000.0,
            vatLiability: 8100.0,
            nextVatPaymentDate: Date().addingTimeInterval(30 * 24 * 60 * 60), // 30 days from now
            openInvoicesCount: 12,
            openInvoicesAmount: 23500.0
        )
    }
    
    private func loadRecentDocuments() async throws -> [DocumentSummary] {
        // This would query the documents table
        // For now, returning mock data
        return [
            DocumentSummary(
                id: "1",
                documentNumber: "INV-2025-001",
                documentType: "tax_invoice",
                customerName: "חברת טכנולוגיה בע\"מ",
                amount: 5000.0,
                currency: "ILS",
                status: "sent",
                issueDate: Date().addingTimeInterval(-2 * 24 * 60 * 60)
            ),
            DocumentSummary(
                id: "2",
                documentNumber: "INV-2025-002",
                documentType: "tax_invoice",
                customerName: "סטארט-אפ חדשני בע\"מ",
                amount: 8500.0,
                currency: "ILS",
                status: "approved",
                issueDate: Date().addingTimeInterval(-1 * 24 * 60 * 60)
            ),
            DocumentSummary(
                id: "3",
                documentNumber: "REC-2025-001",
                documentType: "receipt",
                customerName: "לקוח פרטי",
                amount: 1200.0,
                currency: "ILS",
                status: "paid",
                issueDate: Date()
            )
        ]
    }
    
    private func loadPendingExpenses() async throws -> Int {
        // This would query the expenses table for pending items
        // For now, returning mock data
        return 3
    }
    
    private func updateStats(_ stats: DashboardStats) {
        monthlyRevenue = formatCurrency(stats.monthlyRevenue)
        
        let trendPercentage = ((stats.monthlyRevenue - stats.previousMonthRevenue) / stats.previousMonthRevenue) * 100
        if trendPercentage > 0 {
            revenueTrend = "+\(String(format: "%.1f", trendPercentage))% מהחודש הקודם"
        } else {
            revenueTrend = "\(String(format: "%.1f", trendPercentage))% מהחודש הקודם"
        }
        
        vatLiability = formatCurrency(stats.vatLiability)
        nextPaymentDate = "תשלום עד: \(formatDate(stats.nextVatPaymentDate))"
        
        openInvoicesCount = stats.openInvoicesCount
        openInvoicesAmount = formatCurrency(stats.openInvoicesAmount)
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: amount)) ?? "₪0"
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: date)
    }
}

// MARK: - Data Models

struct DashboardStats {
    let monthlyRevenue: Double
    let previousMonthRevenue: Double
    let vatLiability: Double
    let nextVatPaymentDate: Date
    let openInvoicesCount: Int
    let openInvoicesAmount: Double
}

struct DocumentSummary {
    let id: String
    let documentNumber: String
    let documentType: String
    let customerName: String
    let amount: Double
    let currency: String
    let status: String
    let issueDate: Date
    
    var formattedAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = currency == "ILS" ? "₪" : currency
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: amount)) ?? "₪0"
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: issueDate)
    }
    
    var typeIcon: String {
        switch documentType {
        case "tax_invoice":
            return "doc.text"
        case "receipt":
            return "receipt"
        case "credit_note":
            return "minus.circle"
        case "tax_invoice_receipt":
            return "doc.text.below.ecg"
        default:
            return "doc"
        }
    }
}
