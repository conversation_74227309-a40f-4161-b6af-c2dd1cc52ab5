//
//  AuthViewModel.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import Foundation
import SwiftUI
import Combine

@MainActor
class AuthViewModel: ObservableObject {
    @Published var isAuthenticated = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var currentUser: UserData?
    @Published var userCompanies: [CompanyWithRole] = []
    @Published var selectedCompany: Company?
    
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupAuthStateListener()
    }
    
    // MARK: - Authentication Methods
    
    func signIn(email: String, password: String) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let response = try await supabaseService.signIn(email: email, password: password)
            
            // Fetch user profile after successful login
            await loadUserProfile()
            
            isAuthenticated = true
            
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    func signUp(email: String, password: String) async {
        isLoading = true
        errorMessage = nil
        
        do {
            _ = try await supabaseService.signUp(email: email, password: password)
            // Note: User will need to complete registration with company data
            
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    func completeRegistration(registrationData: RegistrationData) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let response = try await supabaseService.completeRegistration(registrationData: registrationData)
            
            currentUser = response.user
            selectedCompany = response.company
            userCompanies = [CompanyWithRole(
                company: response.company,
                role: "admin",
                joinedAt: response.company.created_at
            )]
            
            isAuthenticated = true
            
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    func signOut() async {
        isLoading = true
        
        do {
            try await supabaseService.signOut()
            
            // Clear user data
            currentUser = nil
            userCompanies = []
            selectedCompany = nil
            isAuthenticated = false
            
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    func checkAuthStatus() async {
        do {
            let session = try await supabaseService.getCurrentSession()
            
            if session != nil {
                await loadUserProfile()
                isAuthenticated = true
            } else {
                isAuthenticated = false
            }
            
        } catch {
            isAuthenticated = false
            errorMessage = error.localizedDescription
        }
    }
    
    // MARK: - User Profile Methods
    
    private func loadUserProfile() async {
        do {
            let profile = try await supabaseService.getUserProfile()
            
            currentUser = profile.user
            userCompanies = profile.companies
            
            // Set the first company as selected if none is selected
            if selectedCompany == nil, let firstCompany = profile.companies.first {
                selectedCompany = firstCompany.company
                saveSelectedCompanyId(firstCompany.company.id)
            }
            
        } catch {
            errorMessage = error.localizedDescription
        }
    }
    
    func selectCompany(_ company: Company) {
        selectedCompany = company
        saveSelectedCompanyId(company.id)
    }
    
    // MARK: - Persistence
    
    private func saveSelectedCompanyId(_ companyId: String) {
        UserDefaults.standard.set(companyId, forKey: Constants.UserDefaults.selectedCompanyId)
    }
    
    private func loadSelectedCompanyId() -> String? {
        return UserDefaults.standard.string(forKey: Constants.UserDefaults.selectedCompanyId)
    }
    
    // MARK: - Auth State Listener
    
    private func setupAuthStateListener() {
        Task {
            for await event in supabaseService.listenToAuthChanges() {
                await handleAuthStateChange(event)
            }
        }
    }
    
    private func handleAuthStateChange(_ event: AuthChangeEvent) async {
        switch event {
        case .signedIn:
            await loadUserProfile()
            isAuthenticated = true
            
        case .signedOut:
            currentUser = nil
            userCompanies = []
            selectedCompany = nil
            isAuthenticated = false
            
        case .tokenRefreshed:
            // Session refreshed, no action needed
            break
            
        default:
            break
        }
    }
    
    // MARK: - Validation Methods
    
    func validateEmail(_ email: String) -> Bool {
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", Constants.Validation.emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    func validatePassword(_ password: String) -> Bool {
        return password.count >= Constants.Validation.passwordMinLength
    }
    
    func validatePhone(_ phone: String) -> Bool {
        let phonePredicate = NSPredicate(format: "SELF MATCHES %@", Constants.Validation.phoneRegex)
        return phonePredicate.evaluate(with: phone)
    }
    
    func validateBusinessNumber(_ businessNumber: String) -> Bool {
        let businessPredicate = NSPredicate(format: "SELF MATCHES %@", Constants.Validation.businessNumberRegex)
        return businessPredicate.evaluate(with: businessNumber)
    }
    
    func validateVatId(_ vatId: String) -> Bool {
        let vatPredicate = NSPredicate(format: "SELF MATCHES %@", Constants.Validation.vatIdRegex)
        return vatPredicate.evaluate(with: vatId)
    }
    
    // MARK: - Error Handling
    
    func clearError() {
        errorMessage = nil
    }
    
    func showError(_ message: String) {
        errorMessage = message
    }
}

// MARK: - Registration Form Data

struct RegistrationFormData {
    // Step 1: Account Information
    var email: String = ""
    var password: String = ""
    var confirmPassword: String = ""
    var fullName: String = ""
    var phone: String = ""
    
    // Step 2: Company Information
    var businessNumber: String = ""
    var companyNameHebrew: String = ""
    var companyNameEnglish: String = ""
    var vatId: String = ""
    var addressHebrew: String = ""
    var cityHebrew: String = ""
    var companyPhone: String = ""
    
    // Step 3: Survey
    var industry: String = ""
    var annualRevenue: String = ""
    var interestedInLoan: Bool = true
    var interestedInInsurance: Bool = true
    var interestedInAccounting: Bool = true
    
    func toRegistrationData() -> RegistrationData {
        return RegistrationData(
            email: email,
            password: password,
            full_name: fullName,
            phone: phone,
            company: CompanyData(
                business_number: businessNumber,
                name_hebrew: companyNameHebrew,
                name_english: companyNameEnglish.isEmpty ? nil : companyNameEnglish,
                vat_id: vatId,
                address_hebrew: addressHebrew,
                city_hebrew: cityHebrew,
                phone: companyPhone,
                industry: industry,
                annual_revenue: annualRevenue.isEmpty ? nil : annualRevenue,
                interested_in_loan: interestedInLoan,
                interested_in_insurance: interestedInInsurance,
                interested_in_accounting: interestedInAccounting
            )
        )
    }
}
