//
//  SupabaseService.swift
//  InvoiceApp
//
//  Created by Fintech Team on 2025-01-29.
//

import Foundation
import Supabase
import Combine

class SupabaseService: ObservableObject {
    static let shared = SupabaseService()
    
    let client: SupabaseClient
    
    private init() {
        // Initialize Supabase client
        // Note: In production, these should come from environment variables or secure configuration
        self.client = SupabaseClient(
            supabaseURL: URL(string: Constants.Supabase.url)!,
            supabaseKey: Constants.Supabase.anonKey
        )
    }
    
    // MARK: - Authentication Methods
    
    /// Sign in with email and password
    func signIn(email: String, password: String) async throws -> AuthResponse {
        do {
            let response = try await client.auth.signIn(
                email: email,
                password: password
            )
            return AuthResponse(
                user: response.user,
                session: response.session
            )
        } catch {
            throw AuthError.loginFailed(error.localizedDescription)
        }
    }
    
    /// Sign up with email and password
    func signUp(email: String, password: String) async throws -> AuthResponse {
        do {
            let response = try await client.auth.signUp(
                email: email,
                password: password,
                redirectTo: Constants.Supabase.redirectURL
            )
            return AuthResponse(
                user: response.user,
                session: response.session
            )
        } catch {
            throw AuthError.registrationFailed(error.localizedDescription)
        }
    }
    
    /// Sign out current user
    func signOut() async throws {
        try await client.auth.signOut()
    }
    
    /// Get current session
    func getCurrentSession() async throws -> Session? {
        return try await client.auth.session
    }
    
    /// Get current user
    func getCurrentUser() async throws -> User? {
        return try await client.auth.user
    }
    
    // MARK: - Registration with Company Data
    
    /// Complete registration with company information
    func completeRegistration(registrationData: RegistrationData) async throws -> RegistrationResponse {
        do {
            // Call the auth-register edge function
            let response: RegistrationResponse = try await client.functions
                .invoke(
                    "auth-register",
                    options: FunctionInvokeOptions(
                        body: registrationData
                    )
                )
            
            return response
        } catch {
            throw AuthError.registrationFailed(error.localizedDescription)
        }
    }
    
    // MARK: - User Profile Methods
    
    /// Get user profile with companies
    func getUserProfile() async throws -> UserProfile {
        guard let user = try await getCurrentUser() else {
            throw AuthError.userNotFound
        }
        
        // Fetch user details
        let userResponse: [UserData] = try await client
            .from("users")
            .select("*")
            .eq("id", value: user.id)
            .execute()
            .value
        
        guard let userData = userResponse.first else {
            throw AuthError.userNotFound
        }
        
        // Fetch user's companies
        let companiesResponse: [CompanyUserData] = try await client
            .from("company_users")
            .select("""
                role,
                created_at,
                company:companies(*)
            """)
            .eq("user_id", value: user.id)
            .execute()
            .value
        
        return UserProfile(
            user: userData,
            companies: companiesResponse.map { companyUser in
                CompanyWithRole(
                    company: companyUser.company,
                    role: companyUser.role,
                    joinedAt: companyUser.created_at
                )
            }
        )
    }
    
    // MARK: - Session Management
    
    /// Listen to auth state changes
    func listenToAuthChanges() -> AsyncStream<AuthChangeEvent> {
        return client.auth.authStateChanges
    }
    
    /// Refresh current session
    func refreshSession() async throws -> Session {
        return try await client.auth.refreshSession()
    }
}

// MARK: - Data Models

struct AuthResponse {
    let user: User
    let session: Session?
}

struct RegistrationData: Codable {
    let email: String
    let password: String
    let full_name: String
    let phone: String
    let company: CompanyData
}

struct CompanyData: Codable {
    let business_number: String
    let name_hebrew: String
    let name_english: String?
    let vat_id: String
    let address_hebrew: String
    let city_hebrew: String
    let phone: String
    let industry: String
    let annual_revenue: String?
    let interested_in_loan: Bool
    let interested_in_insurance: Bool
    let interested_in_accounting: Bool
}

struct RegistrationResponse: Codable {
    let user: UserData
    let company: Company
    let session: SessionData?
}

struct UserData: Codable {
    let id: String
    let email: String
    let full_name: String
    let phone: String?
    let created_at: String
    let last_login_at: String?
}

struct Company: Codable {
    let id: String
    let business_number: String
    let name_hebrew: String
    let name_english: String?
    let vat_id: String
    let address_hebrew: String
    let address_english: String?
    let city_hebrew: String
    let city_english: String?
    let phone: String
    let email: String
    let logo_url: String?
    let subscription_tier: String
    let subscription_expires_at: String?
    let industry: String
    let annual_revenue: String?
    let interested_in_loan: Bool
    let interested_in_insurance: Bool
    let interested_in_accounting: Bool
    let created_at: String
    let updated_at: String
}

struct CompanyUserData: Codable {
    let role: String
    let created_at: String
    let company: Company
}

struct CompanyWithRole {
    let company: Company
    let role: String
    let joinedAt: String
}

struct UserProfile {
    let user: UserData
    let companies: [CompanyWithRole]
}

struct SessionData: Codable {
    let access_token: String
    let refresh_token: String
    let expires_at: Int
    let token_type: String
}

// MARK: - Error Types

enum AuthError: LocalizedError {
    case loginFailed(String)
    case registrationFailed(String)
    case userNotFound
    case sessionExpired
    case networkError
    case invalidCredentials
    case emailAlreadyExists
    case businessNumberAlreadyExists
    
    var errorDescription: String? {
        switch self {
        case .loginFailed(let message):
            return "התחברות נכשלה: \(message)"
        case .registrationFailed(let message):
            return "הרשמה נכשלה: \(message)"
        case .userNotFound:
            return "משתמש לא נמצא"
        case .sessionExpired:
            return "תוקף ההתחברות פג"
        case .networkError:
            return "בעיית רשת. אנא נסה שוב."
        case .invalidCredentials:
            return "פרטי התחברות שגויים"
        case .emailAlreadyExists:
            return "כתובת האימייל כבר קיימת במערכת"
        case .businessNumberAlreadyExists:
            return "מספר העסק כבר קיים במערכת"
        }
    }
}
