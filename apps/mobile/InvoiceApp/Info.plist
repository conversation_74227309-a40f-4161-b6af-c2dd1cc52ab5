<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>he</string>
	<key>CFBundleDisplayName</key>
	<string>חשבוניות</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.business</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchScreen</key>
	<dict>
		<key>UIColorName</key>
		<string>LaunchScreenBackground</string>
		<key>UIImageName</key>
		<string>LaunchScreenIcon</string>
	</dict>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Dark</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.fintech.invoiceapp.auth</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.fintech.invoiceapp</string>
			</array>
		</dict>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>האפליקציה זקוקה לגישה למצלמה כדי לסרוק קבלות והוצאות</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>האפליקציה זקוקה לגישה לתמונות כדי לבחור קבלות מהגלריה</string>
	<key>NSDocumentsFolderUsageDescription</key>
	<string>האפליקציה זקוקה לגישה לקבצים כדי לשמור ולשתף מסמכים</string>
	<key>NSContactsUsageDescription</key>
	<string>האפליקציה זקוקה לגישה לאנשי קשר כדי להוסיף לקוחות בקלות</string>
	<key>NSFaceIDUsageDescription</key>
	<string>השתמש ב-Face ID כדי להתחבר בצורה מאובטחת ומהירה</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>האפליקציה זקוקה לגישה לרשת המקומית כדי להתחבר לשירותי הענן</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Invoice Document</string>
			<key>UTTypeIdentifier</key>
			<string>com.fintech.invoiceapp.invoice</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>invoice</string>
				</array>
			</dict>
		</dict>
	</array>
	<key>CFBundleLocalizations</key>
	<array>
		<string>he</string>
		<string>en</string>
	</array>
	<key>CFBundleDevelopmentRegion</key>
	<string>he</string>
</dict>
</plist>
