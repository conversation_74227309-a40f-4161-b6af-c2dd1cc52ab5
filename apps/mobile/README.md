# InvoiceApp - iOS Mobile Application

## Overview

This is the iOS mobile application for the Israeli B2B invoicing system. The app provides a native iOS experience with the same functionality as the web application, including authentication, document management, expense tracking, and reporting.

## Features

- **Authentication**: Sign-in/sign-up with multi-step survey matching web app
- **Dashboard**: Overview of monthly revenue, VAT liability, and pending items
- **Document Management**: Create and manage invoices, receipts, and credit notes
- **Expense Tracking**: Scan and categorize business expenses
- **RTL Support**: Full Hebrew language support with right-to-left layout
- **Dark Theme**: Minimalist black and white design matching landing page
- **Offline Capability**: Core functionality works offline with sync when online

## Technical Stack

- **Platform**: iOS 15.0+
- **Language**: Swift 5.0
- **UI Framework**: SwiftUI
- **Architecture**: MVVM
- **Backend**: Supabase
- **Dependencies**:
  - Supabase Swift SDK
  - Swift Crypto
  - Swift Log

## Project Structure

```
InvoiceApp/
├── Models/              # Data models
├── ViewModels/          # Business logic and state management
│   ├── AuthViewModel.swift
│   └── DashboardViewModel.swift
├── Views/               # SwiftUI views
│   ├── Auth/
│   │   ├── LoginView.swift
│   │   └── RegisterView.swift
│   └── Dashboard/
│       └── DashboardView.swift
├── Services/            # API and business services
│   └── SupabaseService.swift
└── Utils/               # Utilities and extensions
    ├── Constants.swift
    ├── DesignSystem.swift
    └── Extensions.swift
```

## Setup Instructions

### Prerequisites

- Xcode 15.0 or later
- iOS 15.0+ device or simulator
- Apple Developer Account (for device testing)

### Installation

1. **Open the project**:
   ```bash
   cd apps/mobile
   open InvoiceApp.xcworkspace
   ```

2. **Configure Supabase**:
   - Update `Constants.swift` with your Supabase URL and anon key
   - Ensure the same environment variables are used as in the web app

3. **Install dependencies**:
   - Dependencies are managed through Swift Package Manager
   - Xcode will automatically resolve packages on first build

4. **Configure signing**:
   - Select your development team in project settings
   - Update bundle identifier if needed: `com.fintech.invoiceapp`

5. **Build and run**:
   - Select target device/simulator
   - Press Cmd+R to build and run

### Environment Configuration

Create a `Config.xcconfig` file (not tracked in git) with:

```
SUPABASE_URL = your_supabase_url_here
SUPABASE_ANON_KEY = your_supabase_anon_key_here
```

## Design System

The app follows the same design system as the web application:

### Colors
- **Background**: #141414 (Dark charcoal)
- **Foreground**: #F2F2F2 (Light grey/white)
- **Primary**: #D9D9D9 (Light grey)
- **Card**: #1F1F1F (Slightly lighter charcoal)
- **Border**: #383838 (Medium charcoal)

### Typography
- **Primary Font**: SecularOne (Hebrew)
- **Fallback Font**: Inter (Latin)
- **RTL Support**: Full right-to-left layout

### Components
- **Cosmic Glass**: Translucent cards with subtle borders
- **Cosmic Glow**: Hover effects with white glow
- **Primary Buttons**: Light grey background with dark text
- **Input Fields**: Dark background with light borders

## Authentication Flow

The mobile app implements the same 6-step registration process as the web app:

1. **Company Name**: Basic company information
2. **Annual Revenue**: Revenue tier selection
3. **Account Details**: Email, password, personal info
4. **Company Details**: Business number, VAT ID, etc.
5. **Address**: Company address and industry
6. **Survey**: Interest in loans, insurance, accounting

## Key Features Implementation

### Dashboard
- Monthly revenue with trend indicators
- VAT liability and payment dates
- Open invoices count and amount
- Pending expenses with notification badges
- Quick action buttons for common tasks

### Document Management
- Create invoices, receipts, credit notes
- Customer selection and management
- Line item entry with VAT calculations
- PDF generation and sharing
- Status tracking and updates

### Expense Tracking
- Camera integration for receipt scanning
- OCR text extraction
- Automatic categorization
- Duplicate detection
- Approval workflow

## App Store Compliance

The app is designed for App Store approval with:

- **Privacy**: Proper usage descriptions for camera, photos, contacts
- **Security**: No hardcoded secrets, proper keychain usage
- **Accessibility**: VoiceOver support, proper contrast ratios
- **Localization**: Hebrew and English support
- **Performance**: Optimized for iOS devices

## Testing

### Unit Tests
```bash
# Run unit tests
xcodebuild test -workspace InvoiceApp.xcworkspace -scheme InvoiceApp -destination 'platform=iOS Simulator,name=iPhone 15'
```

### UI Tests
```bash
# Run UI tests
xcodebuild test -workspace InvoiceApp.xcworkspace -scheme InvoiceAppUITests -destination 'platform=iOS Simulator,name=iPhone 15'
```

## Deployment

### TestFlight (Beta)
1. Archive the app in Xcode
2. Upload to App Store Connect
3. Add beta testers
4. Distribute for testing

### App Store Release
1. Ensure all metadata is complete
2. Submit for review
3. Monitor review status
4. Release when approved

## Troubleshooting

### Common Issues

1. **Build Errors**:
   - Clean build folder (Cmd+Shift+K)
   - Reset package caches
   - Check Xcode version compatibility

2. **Supabase Connection**:
   - Verify URL and keys in Constants.swift
   - Check network connectivity
   - Validate API endpoints

3. **Signing Issues**:
   - Update provisioning profiles
   - Check bundle identifier
   - Verify team membership

### Debug Mode

Enable debug logging by setting:
```swift
#if DEBUG
let logLevel = LogLevel.debug
#else
let logLevel = LogLevel.error
#endif
```

## Contributing

1. Follow Swift style guidelines
2. Use SwiftUI best practices
3. Maintain RTL compatibility
4. Test on multiple device sizes
5. Update documentation for new features

## License

This project is part of the fintech application suite. See the main project LICENSE file for details.

## Support

For technical support or questions:
- Check the main project documentation
- Review Supabase integration guides
- Consult iOS development resources
