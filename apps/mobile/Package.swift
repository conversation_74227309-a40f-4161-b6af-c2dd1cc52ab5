// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "InvoiceApp",
    platforms: [
        .iOS(.v15)
    ],
    products: [
        .library(
            name: "InvoiceApp",
            targets: ["InvoiceApp"]
        ),
    ],
    dependencies: [
        .package(url: "https://github.com/supabase/supabase-swift.git", from: "2.5.1"),
        .package(url: "https://github.com/apple/swift-crypto.git", from: "3.0.0"),
        .package(url: "https://github.com/apple/swift-log.git", from: "1.0.0"),
    ],
    targets: [
        .target(
            name: "InvoiceApp",
            dependencies: [
                .product(name: "Supabase", package: "supabase-swift"),
                .product(name: "Crypto", package: "swift-crypto"),
                .product(name: "Logging", package: "swift-log"),
            ]
        ),
        .testTarget(
            name: "InvoiceAppTests",
            dependencies: ["InvoiceApp"]
        ),
    ]
)
